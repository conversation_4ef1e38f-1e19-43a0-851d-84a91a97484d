2025-07-08 16:08:32 [main] INFO  com.pharma.dms.DmsApplication - Starting DmsApplication v1.0.0 using Java 21.0.7 with PID 20968 (D:\learn\java\dms\target\dms-1.0.0.jar started by wus<PERSON>n in D:\learn\java\dms)
2025-07-08 16:08:32 [main] DEBUG com.pharma.dms.DmsApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-07-08 16:08:32 [main] INFO  com.pharma.dms.DmsApplication - The following 1 profile is active: "simple"
2025-07-08 16:08:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-08 16:08:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 329 ms. Found 13 JPA repository interfaces.
2025-07-08 16:08:37 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-07-08 16:08:37 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 16:08:37 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-07-08 16:08:38 [main] INFO  o.a.c.c.C.[.[localhost].[/dms] - Initializing Spring embedded WebApplicationContext
2025-07-08 16:08:38 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5064 ms
2025-07-08 16:08:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-08 16:08:38 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
2025-07-08 16:08:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-08 16:08:38 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-08 16:08:38 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-08 16:08:39 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.3.1.Final
2025-07-08 16:08:39 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-08 16:08:39 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-08 16:08:39 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-08 16:08:42 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists audit_logs cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists course_departments cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists course_roles cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists departments cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists document_categories cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists document_permissions cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists document_tag_mapping cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists document_tags cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists document_versions cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists documents cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists learning_sessions cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists roles cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists session_activities cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_answers cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_assignments cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_attempts cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_courses cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_modules cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_progress cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_question_options cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_questions cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_records cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists user_roles cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists users cascade 
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    create table audit_logs (
        entity_id bigint,
        id bigint generated by default as identity,
        timestamp timestamp(6) not null,
        action varchar(255) not null,
        details TEXT,
        entity_type varchar(255),
        ip_address varchar(255),
        new_values TEXT,
        old_values TEXT,
        severity varchar(255) not null check (severity in ('INFO','WARNING','ERROR','CRITICAL')),
        user_agent varchar(255),
        username varchar(255) not null,
        primary key (id)
    )
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    create table course_departments (
        course_id bigint not null,
        department_id bigint not null,
        primary key (course_id, department_id)
    )
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    create table course_roles (
        course_id bigint not null,
        role_id bigint not null,
        primary key (course_id, role_id)
    )
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    create table departments (
        is_active boolean not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        updated_at timestamp(6),
        version bigint,
        code varchar(255) unique,
        created_by varchar(255),
        description varchar(255),
        name varchar(255) not null unique,
        updated_by varchar(255),
        primary key (id)
    )
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    create table document_categories (
        is_active boolean not null,
        max_file_size_mb integer,
        requires_approval boolean not null,
        retention_period_months integer,
        sort_order integer,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        parent_category_id bigint,
        updated_at timestamp(6),
        version bigint,
        allowed_file_types varchar(255),
        code varchar(255) unique,
        color varchar(255),
        created_by varchar(255),
        description varchar(255),
        icon varchar(255),
        name varchar(255) not null unique,
        updated_by varchar(255),
        primary key (id)
    )
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    create table document_permissions (
        is_active boolean not null,
        created_at timestamp(6) not null,
        document_id bigint not null,
        expires_at timestamp(6),
        granted_at timestamp(6) not null,
        granted_by bigint,
        id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        version bigint,
        created_by varchar(255),
        permission_type varchar(255) not null check (permission_type in ('READ','WRITE','DELETE','APPROVE','ADMIN')),
        updated_by varchar(255),
        primary key (id),
        unique (document_id, user_id)
    )
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    create table document_tag_mapping (
        document_id bigint not null,
        tag_id bigint not null,
        primary key (document_id, tag_id)
    )
2025-07-08 16:08:42 [main] DEBUG org.hibernate.SQL - 
    create table document_tags (
        is_active boolean not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        updated_at timestamp(6),
        usage_count bigint not null,
        version bigint,
        color varchar(255),
        created_by varchar(255),
        description varchar(255),
        name varchar(255) not null unique,
        updated_by varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table document_versions (
        is_archived boolean not null,
        is_current boolean not null,
        major_version integer not null,
        minor_version integer not null,
        patch_version integer,
        training_required boolean,
        approved_at timestamp(6),
        approved_by bigint,
        created_at timestamp(6) not null,
        created_by bigint not null,
        document_id bigint not null,
        download_count bigint,
        effective_date timestamp(6),
        file_size bigint,
        id bigint generated by default as identity,
        retention_date timestamp(6),
        superseded_date timestamp(6),
        view_count bigint,
        change_description TEXT,
        change_reason varchar(255),
        change_type varchar(255) check (change_type in ('MAJOR','MINOR','PATCH','CORRECTION','EDITORIAL')),
        checksum varchar(255),
        file_path varchar(255) not null,
        gmp_impact_assessment TEXT,
        mime_type varchar(255),
        status varchar(255) check (status in ('DRAFT','UNDER_REVIEW','APPROVED','ACTIVE','SUPERSEDED','ARCHIVED','OBSOLETE')),
        version_number varchar(255) not null,
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table documents (
        is_current_version boolean not null,
        is_encrypted boolean not null,
        retention_period_months integer,
        version_number integer not null,
        approval_date timestamp(6),
        approved_by bigint,
        category_id bigint,
        created_at timestamp(6) not null,
        deleted_at timestamp(6),
        deleted_by bigint,
        download_count bigint not null,
        expiry_date timestamp(6),
        file_size bigint not null,
        id bigint generated by default as identity,
        owner_id bigint not null,
        parent_document_id bigint,
        review_date timestamp(6),
        updated_at timestamp(6),
        version bigint,
        view_count bigint not null,
        checksum varchar(255),
        classification varchar(255) not null check (classification in ('PUBLIC','INTERNAL','CONFIDENTIAL','RESTRICTED')),
        created_by varchar(255),
        description varchar(255),
        file_extension varchar(255),
        file_name varchar(255) not null,
        file_path varchar(255) not null,
        mime_type varchar(255),
        original_file_name varchar(255) not null,
        status varchar(255) not null check (status in ('DRAFT','UNDER_REVIEW','APPROVED','PUBLISHED','ARCHIVED','OBSOLETE')),
        title varchar(255) not null,
        updated_by varchar(255),
        version_label varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table learning_sessions (
        active_duration_minutes integer,
        completion_percentage integer,
        document_read_time_seconds integer,
        interaction_count integer,
        is_mobile_device boolean,
        page_views integer,
        pause_count integer,
        pause_duration_minutes integer,
        quiz_attempts integer,
        total_duration_minutes integer,
        video_watch_time_seconds integer,
        created_at timestamp(6) not null,
        current_module_id bigint,
        end_time timestamp(6),
        id bigint generated by default as identity,
        last_heartbeat timestamp(6),
        start_time timestamp(6) not null,
        training_record_id bigint not null,
        updated_at timestamp(6),
        user_id bigint not null,
        version bigint,
        browser_info varchar(255),
        connection_quality varchar(255),
        created_by varchar(255),
        current_position varchar(255),
        device_type varchar(255),
        ip_address varchar(255),
        notes varchar(255),
        screen_resolution varchar(255),
        session_id varchar(255) not null unique,
        status varchar(255) not null check (status in ('ACTIVE','PAUSED','COMPLETED','EXPIRED','TERMINATED')),
        updated_by varchar(255),
        user_agent varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table roles (
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        updated_at timestamp(6),
        version bigint,
        created_by varchar(255),
        description varchar(255),
        name varchar(255) not null unique check (name in ('ROLE_ADMIN','ROLE_QA','ROLE_USER')),
        updated_by varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table session_activities (
        duration_seconds integer,
        score integer,
        activity_time timestamp(6) not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        learning_session_id bigint not null,
        module_id bigint,
        updated_at timestamp(6),
        version bigint,
        activity_type varchar(255) not null check (activity_type in ('SESSION_START','SESSION_END','SESSION_PAUSE','SESSION_RESUME','MODULE_START','MODULE_COMPLETE','MODULE_SKIP','VIDEO_PLAY','VIDEO_PAUSE','VIDEO_SEEK','VIDEO_COMPLETE','DOCUMENT_OPEN','DOCUMENT_SCROLL','DOCUMENT_BOOKMARK','DOCUMENT_COMPLETE','QUIZ_START','QUIZ_SUBMIT','QUIZ_COMPLETE','INTERACTION_CLICK','INTERACTION_HOVER','INTERACTION_SCROLL','BOOKMARK_ADD','BOOKMARK_REMOVE','NOTE_ADD','NOTE_EDIT','NOTE_DELETE','PROGRESS_SAVE','PROGRESS_LOAD','ERROR_OCCURRED','CONNECTION_LOST','CONNECTION_RESTORED','FEEDBACK_SUBMIT','RATING_SUBMIT','HELP_REQUEST','SUPPORT_CONTACT','CUSTOM')),
        content_id varchar(255),
        created_by varchar(255),
        interaction_data varchar(255),
        notes varchar(255),
        position_data varchar(255),
        result varchar(255),
        updated_by varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table training_answers (
        is_correct boolean not null,
        points_earned integer not null,
        time_spent_seconds integer,
        answer_time timestamp(6),
        attempt_id bigint,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        question_id bigint not null,
        updated_at timestamp(6),
        user_id bigint not null,
        version bigint,
        answer_text varchar(255),
        created_by varchar(255),
        selected_option_ids varchar(255),
        updated_by varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table training_assignments (
        actual_completion_hours float(53),
        auto_assigned boolean not null,
        estimated_completion_hours float(53),
        is_mandatory boolean not null,
        notification_sent boolean not null,
        progress_percentage integer,
        reminder_sent boolean not null,
        assigned_by bigint,
        assigned_date timestamp(6) not null,
        completion_date timestamp(6),
        course_id bigint not null,
        created_at timestamp(6) not null,
        due_date timestamp(6),
        id bigint generated by default as identity,
        last_accessed_date timestamp(6),
        last_reminder_date timestamp(6),
        updated_at timestamp(6),
        user_id bigint not null,
        version bigint,
        assignment_reason varchar(255),
        assignment_type varchar(255) check (assignment_type in ('MANUAL','AUTO','BATCH','ROLE_BASED','DEPARTMENT_BASED','MANDATORY','OPTIONAL')),
        batch_id varchar(255),
        created_by varchar(255),
        exemption_reason varchar(255),
        notes varchar(255),
        priority varchar(255) not null check (priority in ('LOW','NORMAL','HIGH','URGENT')),
        status varchar(255) not null check (status in ('ASSIGNED','IN_PROGRESS','COMPLETED','OVERDUE','CANCELLED','EXEMPTED')),
        updated_by varchar(255),
        primary key (id),
        unique (course_id, user_id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table training_attempts (
        attempt_number integer not null,
        is_passed boolean not null,
        percentage_score float(53),
        score integer,
        total_points integer,
        total_time_minutes integer,
        created_at timestamp(6) not null,
        end_time timestamp(6),
        id bigint generated by default as identity,
        start_time timestamp(6) not null,
        training_record_id bigint not null,
        updated_at timestamp(6),
        version bigint,
        created_by varchar(255),
        ip_address varchar(255),
        notes varchar(255),
        status varchar(255) not null check (status in ('IN_PROGRESS','COMPLETED','ABANDONED','TIMED_OUT')),
        updated_by varchar(255),
        user_agent varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table training_courses (
        auto_assign boolean not null,
        duration_minutes integer,
        estimated_hours float(53),
        is_mandatory boolean not null,
        max_attempts integer not null,
        passing_score integer not null,
        validity_period_months integer,
        approval_date timestamp(6),
        approver_id bigint,
        created_at timestamp(6) not null,
        effective_date timestamp(6),
        expiry_date timestamp(6),
        id bigint generated by default as identity,
        instructor_id bigint,
        related_document_id bigint,
        updated_at timestamp(6),
        version bigint,
        language varchar(10),
        content_type varchar(255) check (content_type in ('VIDEO','DOCUMENT','MIXED','EXTERNAL','INTERACTIVE')),
        course_code varchar(255) unique,
        course_type varchar(255) not null check (course_type in ('GENERAL','GMP','SOP','SAFETY','QUALITY','REGULATORY','TECHNICAL','COMPLIANCE')),
        created_by varchar(255),
        description varchar(255),
        difficulty_level varchar(255) check (difficulty_level in ('BEGINNER','INTERMEDIATE','ADVANCED','EXPERT')),
        document_path varchar(255),
        external_link varchar(255),
        learning_objectives varchar(255),
        prerequisites varchar(255),
        status varchar(255) not null check (status in ('DRAFT','UNDER_REVIEW','APPROVED','ACTIVE','INACTIVE','ARCHIVED')),
        tags varchar(255),
        thumbnail_path varchar(255),
        title varchar(255) not null,
        updated_by varchar(255),
        video_url varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table training_modules (
        duration_minutes integer,
        is_active boolean not null,
        is_mandatory boolean not null,
        sort_order integer not null,
        course_id bigint not null,
        created_at timestamp(6) not null,
        file_size bigint,
        id bigint generated by default as identity,
        updated_at timestamp(6),
        version bigint,
        content TEXT,
        content_type varchar(255) not null check (content_type in ('TEXT','VIDEO','AUDIO','PDF','PRESENTATION','INTERACTIVE','QUIZ','DOCUMENT','IMAGE','EXTERNAL_LINK')),
        created_by varchar(255),
        description varchar(255),
        file_name varchar(255),
        file_path varchar(255),
        mime_type varchar(255),
        title varchar(255) not null,
        updated_by varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table training_progress (
        auto_saved boolean,
        difficulty_rating integer,
        document_read_time_seconds integer,
        interaction_count integer,
        is_completed boolean not null,
        is_mobile_device boolean,
        pause_count integer,
        progress_percentage integer not null,
        quiz_attempts integer,
        quiz_score integer,
        satisfaction_rating integer,
        time_spent_minutes integer not null,
        total_pause_time_minutes integer,
        video_watch_time_seconds integer,
        completion_date timestamp(6),
        created_at timestamp(6) not null,
        end_time timestamp(6),
        id bigint generated by default as identity,
        last_accessed timestamp(6),
        last_bookmark_time timestamp(6),
        module_id bigint,
        start_time timestamp(6),
        training_record_id bigint not null,
        updated_at timestamp(6),
        version bigint,
        bookmark_position varchar(255),
        browser_info varchar(255),
        created_by varchar(255),
        device_type varchar(255),
        ip_address varchar(255),
        learning_path varchar(255),
        notes varchar(255),
        session_id varchar(255),
        updated_by varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table training_question_options (
        is_correct boolean not null,
        sort_order integer not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        question_id bigint not null,
        updated_at timestamp(6),
        version bigint,
        created_by varchar(255),
        option_text varchar(255) not null,
        updated_by varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table training_questions (
        is_active boolean not null,
        points integer not null,
        sort_order integer not null,
        course_id bigint,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        module_id bigint,
        updated_at timestamp(6),
        version bigint,
        created_by varchar(255),
        explanation varchar(255),
        question_text varchar(255) not null,
        question_type varchar(255) not null check (question_type in ('MULTIPLE_CHOICE','MULTIPLE_SELECT','TRUE_FALSE','TEXT_INPUT','ESSAY','FILL_IN_BLANK','MATCHING','ORDERING')),
        reference_material varchar(255),
        updated_by varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table training_records (
        attempts_count integer not null,
        final_score integer,
        is_retrain_required boolean not null,
        max_attempts integer not null,
        passing_score integer not null,
        progress_percentage integer not null,
        total_time_minutes integer,
        approval_date timestamp(6),
        approved_by bigint,
        assignment_id bigint,
        certificate_expiry_date timestamp(6),
        certificate_issue_date timestamp(6),
        completion_date timestamp(6),
        course_id bigint not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        instructor_id bigint,
        retrain_due_date timestamp(6),
        signature_date timestamp(6),
        start_date timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint not null,
        version bigint,
        certificate_hash varchar(255),
        certificate_number varchar(255) unique,
        certificate_path varchar(255),
        comments varchar(255),
        created_by varchar(255),
        electronic_signature varchar(255),
        feedback varchar(255),
        retrain_reason varchar(255),
        signature_hash varchar(255),
        signature_ip_address varchar(255),
        signature_user_agent varchar(255),
        status varchar(255) not null check (status in ('NOT_STARTED','IN_PROGRESS','COMPLETED','FAILED','CERTIFIED','EXPIRED','RETRAIN_REQUIRED')),
        updated_by varchar(255),
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table user_roles (
        role_id bigint not null,
        user_id bigint not null,
        primary key (role_id, user_id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    create table users (
        failed_login_attempts integer not null,
        is_active boolean not null,
        is_locked boolean not null,
        password_change_required boolean not null,
        created_at timestamp(6) not null,
        department_id bigint,
        id bigint generated by default as identity,
        last_login timestamp(6),
        updated_at timestamp(6),
        version bigint,
        created_by varchar(255),
        email varchar(255) not null unique,
        first_name varchar(255) not null,
        last_name varchar(255) not null,
        password varchar(255) not null,
        phone varchar(255),
        updated_by varchar(255),
        username varchar(255) not null unique,
        primary key (id)
    )
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists course_departments 
       add constraint FK5m7oy84xgmw2w0brbhbclegjv 
       foreign key (department_id) 
       references departments
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists course_departments 
       add constraint FK13ebthfvem3bl8j9704gs36li 
       foreign key (course_id) 
       references training_courses
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists course_roles 
       add constraint FKhltnkhflux2d4w9seg9s2ohnh 
       foreign key (role_id) 
       references roles
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists course_roles 
       add constraint FKtj1icot83r3xwa0xhhfyfdqdy 
       foreign key (course_id) 
       references training_courses
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists document_categories 
       add constraint FK386luo77etqdjmursi4su7csp 
       foreign key (parent_category_id) 
       references document_categories
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists document_permissions 
       add constraint FK7n2j2k70ihw3dt54pajek4ejk 
       foreign key (document_id) 
       references documents
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists document_permissions 
       add constraint FKacpnlli95jyqd925dfucrewuo 
       foreign key (granted_by) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists document_permissions 
       add constraint FK7mr2sk6ksvu30lprntohhv601 
       foreign key (user_id) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists document_tag_mapping 
       add constraint FK4g0mm06qguxxftml894wx3tod 
       foreign key (tag_id) 
       references document_tags
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists document_tag_mapping 
       add constraint FKqnwurfhqtiwtrjmtawo7fljgl 
       foreign key (document_id) 
       references documents
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists document_versions 
       add constraint FKtf726f7uk5qkm9wupilurryet 
       foreign key (approved_by) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists document_versions 
       add constraint FKff000jpn1ywitfog43rb0ncxv 
       foreign key (created_by) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists document_versions 
       add constraint FKi6p7dgv96b8s8ivf84hqo9pt 
       foreign key (document_id) 
       references documents
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists documents 
       add constraint FKiywhancwwpaggthda6bm8auii 
       foreign key (approved_by) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists documents 
       add constraint FK7dxqwmnshfyh28xnbgiui88cc 
       foreign key (category_id) 
       references document_categories
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists documents 
       add constraint FKstx96y4m8xhsd5odedm6w77jy 
       foreign key (deleted_by) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists documents 
       add constraint FKoduxo6gl9tkyx39jo5kue60bq 
       foreign key (owner_id) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists documents 
       add constraint FK60dm4aap7dopqi9s25b69lhin 
       foreign key (parent_document_id) 
       references documents
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists learning_sessions 
       add constraint FKo3tansvi8yveanvunkru9mbh8 
       foreign key (training_record_id) 
       references training_records
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists learning_sessions 
       add constraint FKc0jfn5lt5ifwhwu8mfb5xm92q 
       foreign key (user_id) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists session_activities 
       add constraint FKo6ddivxxgl0lmu5rmo4b6vud8 
       foreign key (learning_session_id) 
       references learning_sessions
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_answers 
       add constraint FKm8okn28ohhe2khw6h8jgaekx2 
       foreign key (attempt_id) 
       references training_attempts
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_answers 
       add constraint FKjxqphi5bfwgblhlcqx30byns1 
       foreign key (question_id) 
       references training_questions
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_answers 
       add constraint FKewjdw8vw67ftjwoj2ooevokgn 
       foreign key (user_id) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_assignments 
       add constraint FKk04bejs4nqjxxqns9lvlmjamc 
       foreign key (assigned_by) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_assignments 
       add constraint FKr7qejqdy8mo478f37xwuig3pj 
       foreign key (course_id) 
       references training_courses
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_assignments 
       add constraint FKospx0fyaj7mw1qg15v94s8kqa 
       foreign key (user_id) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_attempts 
       add constraint FK19whhoepd7md7galcvwicxcqm 
       foreign key (training_record_id) 
       references training_records
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_courses 
       add constraint FKr910rpw1aww1gsgw7ggx8r1ou 
       foreign key (approver_id) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_courses 
       add constraint FKhvmu7dr0f3d8cxmcbbg84vs18 
       foreign key (instructor_id) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_courses 
       add constraint FKsm8cf435qtgdvfpjfjvv4pqit 
       foreign key (related_document_id) 
       references documents
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_modules 
       add constraint FKmut6x7ek0t4ctjhkw82gmbtle 
       foreign key (course_id) 
       references training_courses
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_progress 
       add constraint FKrgibb9c117qohf8bodyh4it8b 
       foreign key (module_id) 
       references training_modules
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_progress 
       add constraint FKgh37h4m55g4vnkt2dr30r3x8a 
       foreign key (training_record_id) 
       references training_records
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_question_options 
       add constraint FK7daj7h352p36r74yif95gjno 
       foreign key (question_id) 
       references training_questions
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_questions 
       add constraint FKohk5ujph39bxngskewnbf5wvj 
       foreign key (course_id) 
       references training_courses
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_questions 
       add constraint FK5s546f9exq5t7pur0iq7bblbc 
       foreign key (module_id) 
       references training_modules
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_records 
       add constraint FKpaygis26si48gasaa8vb05mn0 
       foreign key (approved_by) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_records 
       add constraint FKevvc4do0qi9wnmw5tt2dnx003 
       foreign key (assignment_id) 
       references training_assignments
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_records 
       add constraint FK6c2eimk3h8k7cr7wrjleawjlw 
       foreign key (course_id) 
       references training_courses
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_records 
       add constraint FK61beu3olu527gm9tmagggh1jt 
       foreign key (instructor_id) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists training_records 
       add constraint FK8s4xw6otg9rmm3iw5nbcw4c62 
       foreign key (user_id) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists user_roles 
       add constraint FKh8ciramu9cc9q3qcqiv4ue8a6 
       foreign key (role_id) 
       references roles
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists user_roles 
       add constraint FKhfh9dx7w3ubf1co1vdev94g3f 
       foreign key (user_id) 
       references users
2025-07-08 16:08:43 [main] DEBUG org.hibernate.SQL - 
    alter table if exists users 
       add constraint FKsbg59w8q63i0oo53rlgvlcnjq 
       foreign key (department_id) 
       references departments
2025-07-08 16:08:43 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-08 16:08:43 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-08 16:08:46 [main] DEBUG c.p.dms.security.AuthTokenFilter - Filter 'authTokenFilter' configured for use
2025-07-08 16:08:46 [main] DEBUG c.p.dms.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-08 16:08:48 [main] INFO  com.pharma.dms.service.OCRService - OCR Service initialized (Tesseract currently disabled)
2025-07-08 16:08:48 [main] INFO  c.pharma.dms.service.OCRServiceNew - Tesseract path set to: D:/ocr
2025-07-08 16:08:48 [main] INFO  c.pharma.dms.service.OCRServiceNew - Tesseract data path set to: D:/ocr/tessdata
2025-07-08 16:08:48 [main] INFO  c.pharma.dms.service.OCRServiceNew - Tesseract language set to: chi_sim+eng
2025-07-08 16:08:49 [main] INFO  c.pharma.dms.service.OCRServiceNew - Tesseract test successful
2025-07-08 16:08:49 [main] INFO  c.pharma.dms.service.OCRServiceNew - Tesseract OCR initialized successfully
2025-07-08 16:08:52 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1c1a014e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6cd8046e, org.springframework.security.web.context.SecurityContextHolderFilter@715d7bbc, org.springframework.security.web.header.HeaderWriterFilter@6cdb9133, org.springframework.web.filter.CorsFilter@7f0c153b, org.springframework.security.web.authentication.logout.LogoutFilter@1e1a4a92, com.pharma.dms.security.AuthTokenFilter@3e5595da, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2a0c8a25, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3ba1113e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@63a8998e, org.springframework.security.web.session.SessionManagementFilter@47547dec, org.springframework.security.web.access.ExceptionTranslationFilter@7c46ceff, org.springframework.security.web.access.intercept.AuthorizationFilter@60d20c17]
2025-07-08 16:08:53 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'handlerExceptionResolver' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Failed to instantiate [org.springframework.web.servlet.HandlerExceptionResolver]: Factory method 'handlerExceptionResolver' threw exception with message: Ambiguous @ExceptionHandler method mapped for [class org.springframework.security.access.AccessDeniedException]: {public org.springframework.http.ResponseEntity com.pharma.dms.exception.GlobalExceptionHandler.handleSecurityException(java.lang.Exception,org.springframework.web.context.request.WebRequest), public org.springframework.http.ResponseEntity com.pharma.dms.exception.GlobalExceptionHandler.handleAccessDeniedException(org.springframework.security.access.AccessDeniedException,org.springframework.web.context.request.WebRequest)}
2025-07-08 16:08:53 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists audit_logs cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists course_departments cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists course_roles cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists departments cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists document_categories cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists document_permissions cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists document_tag_mapping cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists document_tags cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists document_versions cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists documents cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists learning_sessions cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists roles cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists session_activities cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_answers cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_assignments cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_attempts cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_courses cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_modules cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_progress cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_question_options cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_questions cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists training_records cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists user_roles cascade 
2025-07-08 16:08:53 [main] DEBUG org.hibernate.SQL - 
    drop table if exists users cascade 
2025-07-08 16:08:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-08 16:08:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-08 16:08:53 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-08 16:08:53 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-08 16:08:53 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'handlerExceptionResolver' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Failed to instantiate [org.springframework.web.servlet.HandlerExceptionResolver]: Factory method 'handlerExceptionResolver' threw exception with message: Ambiguous @ExceptionHandler method mapped for [class org.springframework.security.access.AccessDeniedException]: {public org.springframework.http.ResponseEntity com.pharma.dms.exception.GlobalExceptionHandler.handleSecurityException(java.lang.Exception,org.springframework.web.context.request.WebRequest), public org.springframework.http.ResponseEntity com.pharma.dms.exception.GlobalExceptionHandler.handleAccessDeniedException(org.springframework.security.access.AccessDeniedException,org.springframework.web.context.request.WebRequest)}
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:655)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:643)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1336)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1166)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:946)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.pharma.dms.DmsApplication.main(DmsApplication.java:12)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:91)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:53)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:58)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.web.servlet.HandlerExceptionResolver]: Factory method 'handlerExceptionResolver' threw exception with message: Ambiguous @ExceptionHandler method mapped for [class org.springframework.security.access.AccessDeniedException]: {public org.springframework.http.ResponseEntity com.pharma.dms.exception.GlobalExceptionHandler.handleSecurityException(java.lang.Exception,org.springframework.web.context.request.WebRequest), public org.springframework.http.ResponseEntity com.pharma.dms.exception.GlobalExceptionHandler.handleAccessDeniedException(org.springframework.security.access.AccessDeniedException,org.springframework.web.context.request.WebRequest)}
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:178)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:651)
	... 24 common frames omitted
Caused by: java.lang.IllegalStateException: Ambiguous @ExceptionHandler method mapped for [class org.springframework.security.access.AccessDeniedException]: {public org.springframework.http.ResponseEntity com.pharma.dms.exception.GlobalExceptionHandler.handleSecurityException(java.lang.Exception,org.springframework.web.context.request.WebRequest), public org.springframework.http.ResponseEntity com.pharma.dms.exception.GlobalExceptionHandler.handleAccessDeniedException(org.springframework.security.access.AccessDeniedException,org.springframework.web.context.request.WebRequest)}
	at org.springframework.web.method.annotation.ExceptionHandlerMethodResolver.addExceptionMapping(ExceptionHandlerMethodResolver.java:114)
	at org.springframework.web.method.annotation.ExceptionHandlerMethodResolver.<init>(ExceptionHandlerMethodResolver.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.initExceptionHandlerAdviceCache(ExceptionHandlerExceptionResolver.java:289)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.afterPropertiesSet(ExceptionHandlerExceptionResolver.java:256)
	at org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport.addDefaultHandlerExceptionResolvers(WebMvcConfigurationSupport.java:1063)
	at org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport.handlerExceptionResolver(WebMvcConfigurationSupport.java:1005)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	... 25 common frames omitted
