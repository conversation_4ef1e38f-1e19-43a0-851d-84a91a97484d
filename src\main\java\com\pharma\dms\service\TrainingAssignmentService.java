package com.pharma.dms.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pharma.dms.entity.AuditLog;
import com.pharma.dms.entity.Department;
import com.pharma.dms.entity.Role;
import com.pharma.dms.entity.TrainingAssignment;
import com.pharma.dms.entity.TrainingCourse;
import com.pharma.dms.entity.User;
import com.pharma.dms.exception.BusinessException;
import com.pharma.dms.repository.DepartmentRepository;
import com.pharma.dms.repository.RoleRepository;
import com.pharma.dms.repository.TrainingAssignmentRepository;
import com.pharma.dms.repository.TrainingCourseRepository;
import com.pharma.dms.repository.UserRepository;

/**
 * 培训分配服务
 * 负责培训任务的分配、撤销、进度跟踪等业务逻辑
 */
@Service
@Transactional
public class TrainingAssignmentService {

    @Autowired
    private TrainingAssignmentRepository assignmentRepository;

    @Autowired
    private TrainingCourseRepository courseRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private AuditService auditService;

    @Autowired
    private EmailService emailService;

    // ========== 基础CRUD操作 ==========

    public List<TrainingAssignment> getAllAssignments() {
        return assignmentRepository.findAll();
    }

    public Page<TrainingAssignment> getAllAssignments(Pageable pageable) {
        return assignmentRepository.findAll(pageable);
    }

    public Optional<TrainingAssignment> getAssignmentById(Long id) {
        return assignmentRepository.findById(id);
    }

    public List<TrainingAssignment> getAssignmentsByUser(User user) {
        return assignmentRepository.findByUser(user);
    }

    public List<TrainingAssignment> getAssignmentsByCourse(TrainingCourse course) {
        return assignmentRepository.findByCourse(course);
    }

    // ========== 单个分配操作 ==========

    /**
     * 分配培训给单个用户
     */
    public TrainingAssignment assignTrainingToUser(Long courseId, Long userId, User assignedBy, 
                                                  LocalDateTime dueDate, String reason, String notes) {
        System.out.println("=== 分配培训给用户 ===");
        System.out.println("课程ID: " + courseId + ", 用户ID: " + userId);

        // 验证课程和用户
        TrainingCourse course = courseRepository.findById(courseId)
                .orElseThrow(() -> new BusinessException("课程不存在"));
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 检查是否已经分配过
        Optional<TrainingAssignment> existingAssignment = assignmentRepository.findByCourseAndUser(course, user);
        if (existingAssignment.isPresent()) {
            throw new BusinessException("该用户已经被分配了此培训课程");
        }

        // 验证课程状态
        if (course.getStatus() != TrainingCourse.CourseStatus.ACTIVE) {
            throw new BusinessException("只能分配活跃状态的课程");
        }

        // 创建分配记录
        TrainingAssignment assignment = new TrainingAssignment(course, user, assignedBy);
        assignment.setDueDate(dueDate);
        assignment.setAssignmentReason(reason);
        assignment.setNotes(notes);
        assignment.setIsMandatory(course.getIsMandatory());
        assignment.setAssignmentType(TrainingAssignment.AssignmentType.MANUAL);
        assignment.setEstimatedCompletionHours(course.getEstimatedHours());

        // 设置优先级
        if (course.getIsMandatory()) {
            assignment.setPriority(TrainingAssignment.Priority.HIGH);
        }

        // 如果没有指定截止日期，使用课程的有效期
        if (dueDate == null && course.getValidityPeriodMonths() != null) {
            assignment.setDueDate(LocalDateTime.now().plusMonths(course.getValidityPeriodMonths()));
        }

        TrainingAssignment savedAssignment = assignmentRepository.save(assignment);

        // 发送通知
        try {
            emailService.sendTrainingAssignmentNotification(savedAssignment);
            savedAssignment.setNotificationSent(true);
            assignmentRepository.save(savedAssignment);
        } catch (Exception e) {
            System.err.println("发送通知失败: " + e.getMessage());
        }

        // 记录审计日志
        auditService.logSystemEvent("TRAINING_ASSIGNED",
                String.format("培训分配: %s -> %s (分配人: %s)", 
                    course.getTitle(), user.getUsername(), 
                    assignedBy != null ? assignedBy.getUsername() : "系统"),
                AuditLog.Severity.INFO);

        System.out.println("✅ 培训分配成功");
        return savedAssignment;
    }

    // ========== 批量分配操作 ==========

    /**
     * 批量分配培训给多个用户
     */
    public BatchAssignmentResult batchAssignTraining(Long courseId, List<Long> userIds, User assignedBy,
                                                   LocalDateTime dueDate, String reason, String notes) {
        System.out.println("=== 批量分配培训 ===");
        System.out.println("课程ID: " + courseId + ", 用户数量: " + userIds.size());

        TrainingCourse course = courseRepository.findById(courseId)
                .orElseThrow(() -> new BusinessException("课程不存在"));

        // 生成批次ID
        String batchId = "BATCH_" + System.currentTimeMillis();
        
        BatchAssignmentResult result = new BatchAssignmentResult();
        result.setBatchId(batchId);
        result.setTotalRequested(userIds.size());

        for (Long userId : userIds) {
            try {
                User user = userRepository.findById(userId)
                        .orElseThrow(() -> new BusinessException("用户不存在: " + userId));

                // 检查是否已经分配过
                Optional<TrainingAssignment> existingAssignment = assignmentRepository.findByCourseAndUser(course, user);
                if (existingAssignment.isPresent()) {
                    result.addSkipped(userId, "已经分配过此课程");
                    continue;
                }

                // 创建分配记录
                TrainingAssignment assignment = new TrainingAssignment(course, user, assignedBy);
                assignment.setBatchId(batchId);
                assignment.setDueDate(dueDate);
                assignment.setAssignmentReason(reason);
                assignment.setNotes(notes);
                assignment.setIsMandatory(course.getIsMandatory());
                assignment.setAssignmentType(TrainingAssignment.AssignmentType.BATCH);
                assignment.setEstimatedCompletionHours(course.getEstimatedHours());

                if (course.getIsMandatory()) {
                    assignment.setPriority(TrainingAssignment.Priority.HIGH);
                }

                if (dueDate == null && course.getValidityPeriodMonths() != null) {
                    assignment.setDueDate(LocalDateTime.now().plusMonths(course.getValidityPeriodMonths()));
                }

                TrainingAssignment savedAssignment = assignmentRepository.save(assignment);
                result.addSuccess(userId);

                // 异步发送通知
                try {
                    emailService.sendTrainingAssignmentNotification(savedAssignment);
                    savedAssignment.setNotificationSent(true);
                    assignmentRepository.save(savedAssignment);
                } catch (Exception e) {
                    System.err.println("发送通知失败 (用户ID: " + userId + "): " + e.getMessage());
                }

            } catch (Exception e) {
                result.addFailed(userId, e.getMessage());
                System.err.println("分配失败 (用户ID: " + userId + "): " + e.getMessage());
            }
        }

        // 记录审计日志
        auditService.logSystemEvent("TRAINING_BATCH_ASSIGNED",
                String.format("批量培训分配: %s, 批次ID: %s, 成功: %d, 失败: %d", 
                    course.getTitle(), batchId, result.getSuccessCount(), result.getFailedCount()),
                AuditLog.Severity.INFO);

        System.out.println("✅ 批量分配完成: 成功 " + result.getSuccessCount() + ", 失败 " + result.getFailedCount());
        return result;
    }

    /**
     * 按部门分配培训
     */
    public BatchAssignmentResult assignTrainingToDepartment(Long courseId, Long departmentId, User assignedBy,
                                                          LocalDateTime dueDate, String reason, String notes) {
        System.out.println("=== 按部门分配培训 ===");

        Department department = departmentRepository.findById(departmentId)
                .orElseThrow(() -> new BusinessException("部门不存在"));

        List<User> departmentUsers = userRepository.findByDepartmentAndIsActiveTrue(department);
        List<Long> userIds = departmentUsers.stream().map(User::getId).collect(Collectors.toList());

        BatchAssignmentResult result = batchAssignTraining(courseId, userIds, assignedBy, dueDate, 
                                                         reason + " (部门分配: " + department.getName() + ")", notes);

        // 更新分配类型
        assignmentRepository.findByBatchId(result.getBatchId()).forEach(assignment -> {
            assignment.setAssignmentType(TrainingAssignment.AssignmentType.DEPARTMENT_BASED);
            assignmentRepository.save(assignment);
        });

        return result;
    }

    /**
     * 按角色分配培训
     */
    public BatchAssignmentResult assignTrainingToRole(Long courseId, String roleName, User assignedBy,
                                                    LocalDateTime dueDate, String reason, String notes) {
        System.out.println("=== 按角色分配培训 ===");

        // 将字符串角色名转换为枚举
        Role.RoleName roleNameEnum;
        try {
            // 处理不同的角色名格式
            switch (roleName.toLowerCase()) {
                case "admin":
                case "role_admin":
                    roleNameEnum = Role.RoleName.ROLE_ADMIN;
                    break;
                case "qa":
                case "role_qa":
                    roleNameEnum = Role.RoleName.ROLE_QA;
                    break;
                case "user":
                case "role_user":
                    roleNameEnum = Role.RoleName.ROLE_USER;
                    break;
                default:
                    throw new BusinessException("不支持的角色名: " + roleName);
            }
        } catch (Exception e) {
            throw new BusinessException("角色名格式错误: " + roleName);
        }

        // 验证角色是否存在
        roleRepository.findByName(roleNameEnum)
                .orElseThrow(() -> new BusinessException("角色不存在: " + roleName));

        // 使用正确的查询方法 - 使用枚举名称
        List<User> roleUsers = userRepository.findByRoleName(roleNameEnum.name());
        // 过滤活跃用户
        roleUsers = roleUsers.stream()
                .filter(User::getIsActive)
                .collect(Collectors.toList());

        List<Long> userIds = roleUsers.stream().map(User::getId).collect(Collectors.toList());

        BatchAssignmentResult result = batchAssignTraining(courseId, userIds, assignedBy, dueDate,
                                                         reason + " (角色分配: " + roleName + ")", notes);

        // 更新分配类型
        assignmentRepository.findByBatchId(result.getBatchId()).forEach(assignment -> {
            assignment.setAssignmentType(TrainingAssignment.AssignmentType.ROLE_BASED);
            assignmentRepository.save(assignment);
        });

        return result;
    }

    // ========== 分配撤销操作 ==========

    /**
     * 撤销培训分配
     */
    public void revokeAssignment(Long assignmentId, User revokedBy, String reason) {
        System.out.println("=== 撤销培训分配 ===");

        TrainingAssignment assignment = assignmentRepository.findById(assignmentId)
                .orElseThrow(() -> new BusinessException("分配记录不存在"));

        // 检查是否可以撤销
        if (assignment.getStatus() == TrainingAssignment.AssignmentStatus.COMPLETED) {
            throw new BusinessException("已完成的培训不能撤销");
        }

        assignment.setStatus(TrainingAssignment.AssignmentStatus.CANCELLED);
        assignment.setNotes(assignment.getNotes() + "\n撤销原因: " + reason);
        assignmentRepository.save(assignment);

        // 记录审计日志
        auditService.logSystemEvent("TRAINING_ASSIGNMENT_REVOKED",
                String.format("培训分配撤销: %s -> %s (撤销人: %s, 原因: %s)",
                    assignment.getCourse().getTitle(), assignment.getUser().getUsername(),
                    revokedBy.getUsername(), reason),
                AuditLog.Severity.WARNING);

        System.out.println("✅ 培训分配撤销成功");
    }

    // ========== 进度跟踪操作 ==========

    /**
     * 更新培训进度
     */
    public void updateProgress(Long assignmentId, Integer progressPercentage, User updatedBy) {
        TrainingAssignment assignment = assignmentRepository.findById(assignmentId)
                .orElseThrow(() -> new BusinessException("分配记录不存在"));

        assignment.setProgressPercentage(progressPercentage);
        assignment.setLastAccessedDate(LocalDateTime.now());

        if (progressPercentage >= 100) {
            assignment.setStatus(TrainingAssignment.AssignmentStatus.COMPLETED);
            assignment.setCompletionDate(LocalDateTime.now());
        } else if (progressPercentage > 0) {
            assignment.setStatus(TrainingAssignment.AssignmentStatus.IN_PROGRESS);
        }

        assignmentRepository.save(assignment);
    }

    // ========== 辅助类 ==========

    public static class BatchAssignmentResult {
        private String batchId;
        private int totalRequested;
        private List<Long> successUserIds = new ArrayList<>();
        private Map<Long, String> skippedUsers = new HashMap<>();
        private Map<Long, String> failedUsers = new HashMap<>();

        // Getters and setters
        public String getBatchId() { return batchId; }
        public void setBatchId(String batchId) { this.batchId = batchId; }
        
        public int getTotalRequested() { return totalRequested; }
        public void setTotalRequested(int totalRequested) { this.totalRequested = totalRequested; }
        
        public int getSuccessCount() { return successUserIds.size(); }
        public int getSkippedCount() { return skippedUsers.size(); }
        public int getFailedCount() { return failedUsers.size(); }
        
        public void addSuccess(Long userId) { successUserIds.add(userId); }
        public void addSkipped(Long userId, String reason) { skippedUsers.put(userId, reason); }
        public void addFailed(Long userId, String reason) { failedUsers.put(userId, reason); }
        
        public List<Long> getSuccessUserIds() { return successUserIds; }
        public Map<Long, String> getSkippedUsers() { return skippedUsers; }
        public Map<Long, String> getFailedUsers() { return failedUsers; }
    }
}
