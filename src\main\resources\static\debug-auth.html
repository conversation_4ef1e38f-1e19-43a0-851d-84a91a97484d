<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
    </style>
</head>
<body>
    <h1>🔍 认证调试工具</h1>
    
    <div class="section">
        <h3>LocalStorage检查</h3>
        <button class="btn-primary" onclick="checkLocalStorage()">检查LocalStorage</button>
        <button class="btn-warning" onclick="clearLocalStorage()">清空LocalStorage</button>
        <div id="localStorageInfo"></div>
    </div>

    <div class="section">
        <h3>手动登录</h3>
        <input type="text" id="username" placeholder="用户名" value="admin">
        <input type="password" id="password" placeholder="密码" value="admin123">
        <button class="btn-success" onclick="manualLogin()">登录</button>
    </div>

    <div class="section">
        <h3>API测试</h3>
        <button class="btn-primary" onclick="testBasicAPI()">测试基础API</button>
        <button class="btn-primary" onclick="testDashboardAPI()">测试仪表板API</button>
        <button class="btn-primary" onclick="testUsersAPI()">测试用户API</button>
        <button class="btn-danger" onclick="clearLog()">清空日志</button>
    </div>

    <div class="section">
        <h3>调试日志</h3>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        let logElement = document.getElementById('debugLog');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            logElement.innerHTML = '';
        }

        function checkLocalStorage() {
            log('=== 检查LocalStorage ===', 'info');
            
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            
            log(`Token: ${token ? token.substring(0, 50) + '...' : 'null'}`, token ? 'success' : 'error');
            log(`User: ${user || 'null'}`, user ? 'success' : 'error');
            log(`IsLoggedIn: ${isLoggedIn || 'null'}`, isLoggedIn === 'true' ? 'success' : 'error');
            
            if (token) {
                try {
                    // 尝试解析JWT token
                    const parts = token.split('.');
                    if (parts.length === 3) {
                        const payload = JSON.parse(atob(parts[1]));
                        log(`Token payload: ${JSON.stringify(payload, null, 2)}`, 'info');
                        
                        const now = Date.now() / 1000;
                        if (payload.exp && payload.exp < now) {
                            log('⚠️ Token已过期', 'warning');
                        } else {
                            log('✅ Token有效', 'success');
                        }
                    }
                } catch (e) {
                    log(`❌ Token解析失败: ${e.message}`, 'error');
                }
            }
            
            document.getElementById('localStorageInfo').innerHTML = `
                <p><strong>Token:</strong> ${token ? '存在' : '不存在'}</p>
                <p><strong>User:</strong> ${user ? '存在' : '不存在'}</p>
                <p><strong>IsLoggedIn:</strong> ${isLoggedIn || '未设置'}</p>
            `;
        }

        function clearLocalStorage() {
            localStorage.clear();
            log('✅ LocalStorage已清空', 'success');
            checkLocalStorage();
        }

        async function manualLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            log(`=== 手动登录: ${username} ===`, 'info');
            
            try {
                const response = await fetch('/dms/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                log(`登录响应状态: ${response.status}`, 'info');
                
                const responseText = await response.text();
                log(`登录响应内容: ${responseText}`, 'info');
                
                if (response.ok) {
                    const result = JSON.parse(responseText);
                    if (result.success) {
                        const token = result.data.token;
                        const user = result.data.user;
                        
                        localStorage.setItem('token', token);
                        localStorage.setItem('user', JSON.stringify(user));
                        localStorage.setItem('isLoggedIn', 'true');
                        
                        log('✅ 登录成功，已保存到LocalStorage', 'success');
                        checkLocalStorage();
                    } else {
                        log(`❌ 登录失败: ${result.message}`, 'error');
                    }
                } else {
                    log(`❌ 登录请求失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`💥 登录异常: ${error.message}`, 'error');
            }
        }

        async function testBasicAPI() {
            log('=== 测试基础API ===', 'info');
            
            try {
                const response = await fetch('/dms/api/test/basic');
                log(`基础API响应状态: ${response.status}`, 'info');
                
                const responseText = await response.text();
                log(`基础API响应: ${responseText}`, response.ok ? 'success' : 'error');
            } catch (error) {
                log(`💥 基础API异常: ${error.message}`, 'error');
            }
        }

        async function testDashboardAPI() {
            log('=== 测试仪表板API ===', 'info');
            
            const token = localStorage.getItem('token');
            if (!token) {
                log('❌ 没有token，请先登录', 'error');
                return;
            }
            
            try {
                const response = await fetch('/dms/api/dashboard/stats', {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`仪表板API响应状态: ${response.status}`, 'info');
                
                const responseText = await response.text();
                log(`仪表板API响应长度: ${responseText.length}`, 'info');
                log(`仪表板API响应: ${responseText}`, response.ok ? 'success' : 'error');
                
                if (response.ok && responseText) {
                    try {
                        const result = JSON.parse(responseText);
                        log('✅ JSON解析成功', 'success');
                    } catch (jsonError) {
                        log(`❌ JSON解析失败: ${jsonError.message}`, 'error');
                    }
                }
            } catch (error) {
                log(`💥 仪表板API异常: ${error.message}`, 'error');
            }
        }

        async function testUsersAPI() {
            log('=== 测试用户API ===', 'info');
            
            const token = localStorage.getItem('token');
            if (!token) {
                log('❌ 没有token，请先登录', 'error');
                return;
            }
            
            try {
                const response = await fetch('/dms/api/users', {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`用户API响应状态: ${response.status}`, 'info');
                
                const responseText = await response.text();
                log(`用户API响应长度: ${responseText.length}`, 'info');
                log(`用户API响应: ${responseText.substring(0, 500)}${responseText.length > 500 ? '...' : ''}`, response.ok ? 'success' : 'error');
                
                if (response.ok && responseText) {
                    try {
                        const result = JSON.parse(responseText);
                        log('✅ JSON解析成功', 'success');
                    } catch (jsonError) {
                        log(`❌ JSON解析失败: ${jsonError.message}`, 'error');
                    }
                }
            } catch (error) {
                log(`💥 用户API异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', function() {
            log('🚀 认证调试工具加载完成', 'info');
            checkLocalStorage();
        });
    </script>
</body>
</html>
