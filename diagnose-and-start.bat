@echo off
chcp 65001 >nul
echo ========================================
echo    DMS系统诊断和启动脚本
echo ========================================
echo.

echo [1/8] 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java未正确安装或配置
    pause
    exit /b 1
)
echo ✅ Java环境正常
echo.

echo [2/8] 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo ❌ Maven未正确安装或配置
    pause
    exit /b 1
)
echo ✅ Maven环境正常
echo.

echo [3/8] 检查项目目录...
if not exist "pom.xml" (
    echo ❌ 当前目录不是Maven项目根目录
    echo 请确保在 D:\learn\java\dms 目录下运行此脚本
    pause
    exit /b 1
)
echo ✅ 项目目录正确
echo.

echo [4/8] 终止现有Java进程...
taskkill /F /IM java.exe 2>nul
if %errorlevel% equ 0 (
    echo ✅ 已终止现有Java进程
) else (
    echo ℹ️ 没有运行中的Java进程
)
echo.

echo [5/8] 清理并编译项目...
mvn clean compile -q
if %errorlevel% neq 0 (
    echo ❌ 项目编译失败
    echo 请检查代码是否有语法错误
    pause
    exit /b 1
)
echo ✅ 项目编译成功
echo.

echo [6/8] 检查端口8081是否被占用...
netstat -ano | findstr :8081 >nul
if %errorlevel% equ 0 (
    echo ⚠️ 端口8081已被占用，尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8081') do (
        taskkill /F /PID %%a 2>nul
    )
)
echo ✅ 端口8081已准备就绪
echo.

echo [7/8] 启动DMS应用...
echo 使用H2内存数据库（简化配置）
echo 应用将在 http://localhost:8081/dms 启动
echo.
echo 启动中，请稍候...
start /B mvn spring-boot:run -Dspring-boot.run.profiles=simple

echo [8/8] 等待应用启动...
timeout /t 10 /nobreak >nul

echo.
echo 正在检查应用状态...
for /L %%i in (1,1,30) do (
    netstat -ano | findstr :8081 >nul
    if !errorlevel! equ 0 (
        echo ✅ 应用启动成功！
        echo.
        echo 🌐 访问地址：
        echo    - 主页: http://localhost:8081/dms
        echo    - 登录: http://localhost:8081/dms/login
        echo    - H2控制台: http://localhost:8081/dms/h2-console
        echo.
        echo 📋 默认登录信息：
        echo    - 管理员: admin / admin123
        echo    - QA用户: qa / qa123
        echo    - 普通用户: user / user123
        echo.
        goto :success
    )
    timeout /t 2 /nobreak >nul
)

echo ❌ 应用启动超时或失败
echo 请检查控制台输出中的错误信息
echo.

:success
echo ========================================
echo 按任意键退出...
pause >nul
