package com.pharma.dms.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.DocumentCategory;
import com.pharma.dms.entity.User;

@Repository
public interface DocumentRepository extends JpaRepository<Document, Long> {

    // Basic queries
    List<Document> findByOwner(User owner);
    
    List<Document> findByCategory(DocumentCategory category);
    
    List<Document> findByStatus(Document.DocumentStatus status);
    
    List<Document> findByIsCurrentVersionTrue();
    
    Optional<Document> findByFileName(String fileName);
    
    List<Document> findByOriginalFileNameContainingIgnoreCase(String fileName);

    // Version control queries
    List<Document> findByParentDocumentOrderByVersionNumberDesc(Document parentDocument);
    
    @Query("SELECT d FROM Document d WHERE d.parentDocument IS NULL AND d.isCurrentVersion = true")
    List<Document> findCurrentVersionDocuments();
    
    @Query("SELECT d FROM Document d WHERE d.parentDocument.id = :parentId ORDER BY d.versionNumber DESC")
    List<Document> findVersionsByParentId(@Param("parentId") Long parentId);

    // Search queries
    @Query("SELECT d FROM Document d WHERE " +
           "(:title IS NULL OR LOWER(d.title) LIKE LOWER(CONCAT('%', :title, '%'))) AND " +
           "(:description IS NULL OR LOWER(d.description) LIKE LOWER(CONCAT('%', :description, '%'))) AND " +
           "(:fileName IS NULL OR LOWER(d.originalFileName) LIKE LOWER(CONCAT('%', :fileName, '%'))) AND " +
           "(:categoryId IS NULL OR d.category.id = :categoryId) AND " +
           "(:ownerId IS NULL OR d.owner.id = :ownerId) AND " +
           "(:status IS NULL OR d.status = :status) AND " +
           "(:classification IS NULL OR d.classification = :classification) AND " +
           "(:isCurrentVersion IS NULL OR d.isCurrentVersion = :isCurrentVersion)")
    Page<Document> findDocumentsWithFilters(@Param("title") String title,
                                           @Param("description") String description,
                                           @Param("fileName") String fileName,
                                           @Param("categoryId") Long categoryId,
                                           @Param("ownerId") Long ownerId,
                                           @Param("status") Document.DocumentStatus status,
                                           @Param("classification") Document.DocumentClassification classification,
                                           @Param("isCurrentVersion") Boolean isCurrentVersion,
                                           Pageable pageable);

    // Tag-based queries
    @Query("SELECT d FROM Document d JOIN d.tags t WHERE t.name = :tagName")
    List<Document> findByTagName(@Param("tagName") String tagName);
    
    @Query("SELECT d FROM Document d JOIN d.tags t WHERE t.id IN :tagIds GROUP BY d HAVING COUNT(t) = :tagCount")
    List<Document> findByAllTags(@Param("tagIds") List<Long> tagIds, @Param("tagCount") long tagCount);

    // Date-based queries
    List<Document> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<Document> findByExpiryDateBefore(LocalDateTime date);
    
    List<Document> findByReviewDateBefore(LocalDateTime date);
    
    @Query("SELECT d FROM Document d WHERE d.reviewDate BETWEEN :startDate AND :endDate")
    List<Document> findDocumentsForReview(@Param("startDate") LocalDateTime startDate, 
                                         @Param("endDate") LocalDateTime endDate);

    // Statistics queries
    @Query("SELECT COUNT(d) FROM Document d WHERE d.owner.id = :ownerId")
    Long countByOwnerId(@Param("ownerId") Long ownerId);
    
    @Query("SELECT COUNT(d) FROM Document d WHERE d.category.id = :categoryId")
    Long countByCategoryId(@Param("categoryId") Long categoryId);
    
    @Query("SELECT COUNT(d) FROM Document d WHERE d.status = :status")
    Long countByStatus(@Param("status") Document.DocumentStatus status);
    
    @Query("SELECT SUM(d.fileSize) FROM Document d WHERE d.owner.id = :ownerId")
    Long getTotalFileSizeByOwner(@Param("ownerId") Long ownerId);
    
    @Query("SELECT SUM(d.downloadCount) FROM Document d")
    Long getTotalDownloadCount();
    
    @Query("SELECT SUM(d.viewCount) FROM Document d")
    Long getTotalViewCount();

    // Recent activity queries
    @Query("SELECT d FROM Document d WHERE d.createdAt >= :since ORDER BY d.createdAt DESC")
    List<Document> findRecentlyCreated(@Param("since") LocalDateTime since, Pageable pageable);
    
    @Query("SELECT d FROM Document d WHERE d.updatedAt >= :since ORDER BY d.updatedAt DESC")
    List<Document> findRecentlyUpdated(@Param("since") LocalDateTime since, Pageable pageable);
    
    @Query("SELECT d FROM Document d ORDER BY d.downloadCount DESC")
    List<Document> findMostDownloaded(Pageable pageable);
    
    @Query("SELECT d FROM Document d ORDER BY d.viewCount DESC")
    List<Document> findMostViewed(Pageable pageable);

    // Department-based queries
    @Query("SELECT d FROM Document d WHERE d.owner.department.id = :departmentId")
    List<Document> findByOwnerDepartment(@Param("departmentId") Long departmentId);
    
    @Query("SELECT COUNT(d) FROM Document d WHERE d.owner.department.id = :departmentId")
    Long countByOwnerDepartment(@Param("departmentId") Long departmentId);

    // Approval workflow queries
    List<Document> findByApprovedBy(User approvedBy);
    
    @Query("SELECT d FROM Document d WHERE d.status = 'UNDER_REVIEW' ORDER BY d.createdAt ASC")
    List<Document> findPendingApproval();
    
    @Query("SELECT COUNT(d) FROM Document d WHERE d.status = 'UNDER_REVIEW'")
    Long countPendingApproval();

    // File management queries
    @Query("SELECT d FROM Document d WHERE d.checksum = :checksum")
    List<Document> findByChecksum(@Param("checksum") String checksum);
    
    @Query("SELECT d FROM Document d WHERE d.isEncrypted = true")
    List<Document> findEncryptedDocuments();
    
    @Query("SELECT d FROM Document d WHERE d.fileSize > :sizeInBytes")
    List<Document> findLargeDocuments(@Param("sizeInBytes") Long sizeInBytes);

    // 优化的分页查询 - 先获取ID，再批量获取详细信息
    @Query("SELECT d.id FROM Document d ORDER BY d.createdAt DESC")
    Page<Long> findDocumentIds(Pageable pageable);

    // 批量获取文档详细信息，避免N+1查询
    @Query("SELECT DISTINCT d FROM Document d " +
           "LEFT JOIN FETCH d.category " +
           "LEFT JOIN FETCH d.owner " +
           "LEFT JOIN FETCH d.approvedBy " +
           "WHERE d.id IN :ids " +
           "ORDER BY d.createdAt DESC")
    List<Document> findByIdsWithAssociations(@Param("ids") List<Long> ids);

    // 优化的搜索查询 - 避免N+1问题
    @Query("SELECT DISTINCT d FROM Document d " +
           "LEFT JOIN FETCH d.category c " +
           "LEFT JOIN FETCH d.owner o " +
           "LEFT JOIN FETCH d.approvedBy a " +
           "WHERE (:title IS NULL OR LOWER(d.title) LIKE LOWER(CONCAT('%', :title, '%'))) AND " +
           "(:description IS NULL OR LOWER(d.description) LIKE LOWER(CONCAT('%', :description, '%'))) AND " +
           "(:fileName IS NULL OR LOWER(d.originalFileName) LIKE LOWER(CONCAT('%', :fileName, '%'))) AND " +
           "(:categoryId IS NULL OR c.id = :categoryId) AND " +
           "(:ownerId IS NULL OR o.id = :ownerId) AND " +
           "(:status IS NULL OR d.status = :status) AND " +
           "(:classification IS NULL OR d.classification = :classification) AND " +
           "(:isCurrentVersion IS NULL OR d.isCurrentVersion = :isCurrentVersion) " +
           "ORDER BY d.createdAt DESC")
    List<Document> findDocumentsWithFiltersOptimized(@Param("title") String title,
                                                   @Param("description") String description,
                                                   @Param("fileName") String fileName,
                                                   @Param("categoryId") Long categoryId,
                                                   @Param("ownerId") Long ownerId,
                                                   @Param("status") Document.DocumentStatus status,
                                                   @Param("classification") Document.DocumentClassification classification,
                                                   @Param("isCurrentVersion") Boolean isCurrentVersion,
                                                   Pageable pageable);

    // 解决懒加载问题的查询 - 分两步查询避免分页问题（保留兼容性）
    @Query("SELECT DISTINCT d FROM Document d " +
           "LEFT JOIN FETCH d.category " +
           "LEFT JOIN FETCH d.owner " +
           "LEFT JOIN FETCH d.approvedBy " +
           "WHERE d.id IN (" +
           "  SELECT d2.id FROM Document d2 " +
           "  ORDER BY d2.createdAt DESC" +
           ")")
    Page<Document> findAllWithAssociations(Pageable pageable);

    // 按状态查询文档
    Page<Document> findByStatus(Document.DocumentStatus status, Pageable pageable);
}
