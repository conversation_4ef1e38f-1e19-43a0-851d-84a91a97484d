<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档上传诊断工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; white-space: pre-wrap; font-family: monospace; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
        .warning { background: #fff3e0; color: #f57c00; }
        .info { background: #e3f2fd; color: #1976d2; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; border: none; }
        .btn-success { background: #28a745; color: white; border: none; }
        .btn-warning { background: #ffc107; color: black; border: none; }
        .btn-danger { background: #dc3545; color: white; border: none; }
        input[type="file"] { margin: 10px 0; padding: 5px; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <h1>🔍 DMS文档上传诊断工具</h1>
    
    <div class="section">
        <h2>步骤1: 系统状态检查</h2>
        <button class="btn-primary" onclick="checkSystemStatus()">检查系统状态</button>
        <div id="systemStatus" class="result"></div>
    </div>

    <div class="section">
        <h2>步骤2: 认证状态检查</h2>
        <button class="btn-primary" onclick="checkAuthStatus()">检查认证状态</button>
        <button class="btn-success" onclick="performLogin()">执行登录</button>
        <div id="authStatus" class="result"></div>
    </div>

    <div class="section">
        <h2>步骤3: API端点测试</h2>
        <button class="btn-primary" onclick="testUploadEndpoint()">测试上传端点</button>
        <button class="btn-warning" onclick="testDocumentList()">测试文档列表</button>
        <div id="apiTest" class="result"></div>
    </div>

    <div class="section">
        <h2>步骤4: 文件上传测试</h2>
        <input type="file" id="diagFile" accept=".txt,.pdf,.doc,.docx">
        <br>
        <button class="btn-success" onclick="performDetailedUpload()">详细上传测试</button>
        <button class="btn-danger" onclick="performRawUpload()">原始上传测试</button>
        <div id="uploadTest" class="result"></div>
    </div>

    <div class="section">
        <h2>步骤5: 网络和浏览器诊断</h2>
        <button class="btn-primary" onclick="networkDiagnosis()">网络诊断</button>
        <button class="btn-warning" onclick="browserDiagnosis()">浏览器诊断</button>
        <div id="networkDiag" class="result"></div>
    </div>

    <script>
        let authToken = null;
        let diagResults = {};

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = 'result ' + type;
            console.log(message);
        }

        function addLog(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.innerHTML += `[${timestamp}] ${message}\n`;
            element.className = 'result ' + type;
            console.log(message);
        }

        async function checkSystemStatus() {
            log('systemStatus', '正在检查系统状态...', 'warning');
            
            let status = '=== 系统状态检查 ===\n\n';
            
            // 检查页面基本信息
            status += `当前URL: ${window.location.href}\n`;
            status += `用户代理: ${navigator.userAgent}\n`;
            status += `页面标题: ${document.title}\n`;
            status += `当前时间: ${new Date().toLocaleString()}\n\n`;

            // 检查必要的脚本
            status += '脚本加载状态:\n';
            status += `- authUtils: ${typeof window.authUtils !== 'undefined' ? '✅ 已加载' : '❌ 未加载'}\n`;
            status += `- fetch API: ${typeof fetch !== 'undefined' ? '✅ 支持' : '❌ 不支持'}\n`;
            status += `- FormData: ${typeof FormData !== 'undefined' ? '✅ 支持' : '❌ 不支持'}\n\n`;

            // 检查本地存储
            status += '本地存储:\n';
            try {
                const token = localStorage.getItem('token');
                const user = localStorage.getItem('user');
                status += `- token: ${token ? '✅ 存在 (' + token.length + ' 字符)' : '❌ 不存在'}\n`;
                status += `- user: ${user ? '✅ 存在' : '❌ 不存在'}\n`;
            } catch (e) {
                status += `- 本地存储访问失败: ${e.message}\n`;
            }

            diagResults.systemStatus = 'OK';
            log('systemStatus', status, 'success');
        }

        async function checkAuthStatus() {
            log('authStatus', '正在检查认证状态...', 'warning');
            
            let status = '=== 认证状态检查 ===\n\n';
            
            try {
                // 检查authUtils
                if (typeof window.authUtils !== 'undefined') {
                    status += 'AuthUtils状态:\n';
                    status += `- isLoggedIn: ${window.authUtils.isLoggedIn()}\n`;
                    
                    const currentUser = window.authUtils.getCurrentUser();
                    status += `- currentUser: ${currentUser ? currentUser.username : '无'}\n`;
                    
                    const headers = window.authUtils.getAuthHeaders();
                    status += `- authHeaders: ${headers.Authorization ? '✅ 存在' : '❌ 不存在'}\n\n`;
                } else {
                    status += '❌ AuthUtils未加载\n\n';
                }

                // 直接检查token
                const token = localStorage.getItem('token');
                if (token) {
                    authToken = token;
                    status += `Token详情:\n`;
                    status += `- 长度: ${token.length}\n`;
                    status += `- 前缀: ${token.substring(0, 30)}...\n`;
                    status += `- 格式: ${token.startsWith('eyJ') ? '✅ JWT格式' : '❌ 非JWT格式'}\n\n`;
                } else {
                    status += '❌ 未找到Token\n\n';
                }

                diagResults.authStatus = token ? 'OK' : 'FAIL';
                log('authStatus', status, token ? 'success' : 'error');
            } catch (error) {
                status += `❌ 认证检查异常: ${error.message}\n`;
                log('authStatus', status, 'error');
            }
        }

        async function performLogin() {
            log('authStatus', '正在执行登录...', 'warning');
            
            try {
                const response = await fetch('/dms/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                let result = '=== 登录测试结果 ===\n\n';
                result += `响应状态: ${response.status}\n`;
                result += `响应头: ${JSON.stringify([...response.headers.entries()], null, 2)}\n\n`;

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.data.token;
                    localStorage.setItem('token', authToken);
                    localStorage.setItem('user', JSON.stringify(data.data));
                    
                    result += `✅ 登录成功!\n`;
                    result += `用户: ${data.data.username}\n`;
                    result += `Token: ${authToken.substring(0, 30)}...\n`;
                    result += `角色: ${data.data.roles.join(', ')}\n`;
                    
                    diagResults.login = 'OK';
                    log('authStatus', result, 'success');
                } else {
                    const errorText = await response.text();
                    result += `❌ 登录失败: ${errorText}\n`;
                    log('authStatus', result, 'error');
                }
            } catch (error) {
                log('authStatus', `❌ 登录异常: ${error.message}`, 'error');
            }
        }

        async function testUploadEndpoint() {
            if (!authToken) {
                log('apiTest', '❌ 请先登录', 'error');
                return;
            }

            log('apiTest', '正在测试上传端点...', 'warning');
            
            try {
                // 测试OPTIONS请求
                const optionsResponse = await fetch('/dms/api/documents/upload', {
                    method: 'OPTIONS',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    }
                });

                let result = '=== 上传端点测试 ===\n\n';
                result += `OPTIONS请求状态: ${optionsResponse.status}\n`;
                result += `允许的方法: ${optionsResponse.headers.get('Allow') || '未指定'}\n`;
                result += `CORS头: ${optionsResponse.headers.get('Access-Control-Allow-Origin') || '未设置'}\n\n`;

                // 测试GET请求（应该返回405）
                const getResponse = await fetch('/dms/api/documents/upload', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    }
                });

                result += `GET请求状态: ${getResponse.status} (期望405)\n`;
                result += `端点可访问性: ${getResponse.status === 405 ? '✅ 正常' : '❌ 异常'}\n\n`;

                log('apiTest', result, 'info');
            } catch (error) {
                log('apiTest', `❌ 端点测试异常: ${error.message}`, 'error');
            }
        }

        async function testDocumentList() {
            if (!authToken) {
                addLog('apiTest', '❌ 请先登录', 'error');
                return;
            }

            addLog('apiTest', '正在测试文档列表API...', 'warning');
            
            try {
                const response = await fetch('/dms/api/documents?page=0&size=5', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    }
                });

                let result = `文档列表API测试:\n`;
                result += `状态: ${response.status}\n`;

                if (response.ok) {
                    const data = await response.json();
                    result += `✅ 成功获取文档列表\n`;
                    result += `总数: ${data.data.totalElements}\n`;
                    result += `当前页: ${data.data.content.length} 个文档\n\n`;
                } else {
                    const errorText = await response.text();
                    result += `❌ 失败: ${errorText}\n\n`;
                }

                addLog('apiTest', result, response.ok ? 'success' : 'error');
            } catch (error) {
                addLog('apiTest', `❌ 文档列表测试异常: ${error.message}\n`, 'error');
            }
        }

        async function performDetailedUpload() {
            if (!authToken) {
                log('uploadTest', '❌ 请先登录', 'error');
                return;
            }

            const fileInput = document.getElementById('diagFile');
            if (!fileInput.files[0]) {
                log('uploadTest', '❌ 请选择文件', 'error');
                return;
            }

            log('uploadTest', '正在执行详细上传测试...', 'warning');
            
            const file = fileInput.files[0];
            let result = '=== 详细上传测试 ===\n\n';
            
            try {
                // 文件信息
                result += `文件信息:\n`;
                result += `- 名称: ${file.name}\n`;
                result += `- 大小: ${file.size} 字节\n`;
                result += `- 类型: ${file.type}\n`;
                result += `- 最后修改: ${new Date(file.lastModified).toLocaleString()}\n\n`;

                // 创建FormData
                const formData = new FormData();
                formData.append('file', file);
                formData.append('title', '诊断测试文档 - ' + new Date().toLocaleTimeString());
                formData.append('description', '这是一个诊断工具生成的测试文档');

                result += `FormData内容:\n`;
                for (let [key, value] of formData.entries()) {
                    if (value instanceof File) {
                        result += `- ${key}: [File] ${value.name} (${value.size} bytes)\n`;
                    } else {
                        result += `- ${key}: ${value}\n`;
                    }
                }
                result += '\n';

                // 发送请求
                result += `发送请求...\n`;
                const startTime = Date.now();
                
                const response = await fetch('/dms/api/documents/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    },
                    body: formData
                });

                const endTime = Date.now();
                result += `请求耗时: ${endTime - startTime}ms\n`;
                result += `响应状态: ${response.status}\n`;
                result += `响应头:\n`;
                for (let [key, value] of response.headers.entries()) {
                    result += `  ${key}: ${value}\n`;
                }
                result += '\n';

                if (response.ok) {
                    const data = await response.json();
                    result += `✅ 上传成功!\n`;
                    result += `响应数据:\n${JSON.stringify(data, null, 2)}\n`;
                    log('uploadTest', result, 'success');
                } else {
                    const errorText = await response.text();
                    result += `❌ 上传失败:\n${errorText}\n`;
                    log('uploadTest', result, 'error');
                }
            } catch (error) {
                result += `❌ 上传异常: ${error.message}\n`;
                result += `错误堆栈:\n${error.stack}\n`;
                log('uploadTest', result, 'error');
            }
        }

        async function performRawUpload() {
            if (!authToken) {
                addLog('uploadTest', '❌ 请先登录', 'error');
                return;
            }

            const fileInput = document.getElementById('diagFile');
            if (!fileInput.files[0]) {
                addLog('uploadTest', '❌ 请选择文件', 'error');
                return;
            }

            addLog('uploadTest', '正在执行原始上传测试...', 'warning');
            
            const file = fileInput.files[0];
            
            try {
                // 使用XMLHttpRequest进行原始上传
                const xhr = new XMLHttpRequest();
                const formData = new FormData();
                formData.append('file', file);
                formData.append('title', '原始上传测试 - ' + new Date().toLocaleTimeString());
                formData.append('description', '使用XMLHttpRequest的原始上传测试');

                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        addLog('uploadTest', `上传进度: ${percentComplete.toFixed(2)}%`, 'info');
                    }
                });

                xhr.addEventListener('load', () => {
                    let result = `原始上传结果:\n`;
                    result += `状态: ${xhr.status}\n`;
                    result += `响应: ${xhr.responseText}\n\n`;
                    addLog('uploadTest', result, xhr.status === 200 ? 'success' : 'error');
                });

                xhr.addEventListener('error', () => {
                    addLog('uploadTest', `❌ 原始上传网络错误\n`, 'error');
                });

                xhr.open('POST', '/dms/api/documents/upload');
                xhr.setRequestHeader('Authorization', 'Bearer ' + authToken);
                xhr.send(formData);

            } catch (error) {
                addLog('uploadTest', `❌ 原始上传异常: ${error.message}\n`, 'error');
            }
        }

        async function networkDiagnosis() {
            log('networkDiag', '正在进行网络诊断...', 'warning');
            
            let result = '=== 网络诊断 ===\n\n';
            
            // 检查网络连接
            result += `网络状态: ${navigator.onLine ? '✅ 在线' : '❌ 离线'}\n`;
            result += `连接类型: ${navigator.connection ? navigator.connection.effectiveType : '未知'}\n\n`;

            // 测试基本连接
            try {
                const startTime = Date.now();
                const response = await fetch('/dms/api/auth/check', {
                    method: 'GET',
                    headers: authToken ? { 'Authorization': 'Bearer ' + authToken } : {}
                });
                const endTime = Date.now();
                
                result += `基本连接测试:\n`;
                result += `- 延迟: ${endTime - startTime}ms\n`;
                result += `- 状态: ${response.status}\n`;
                result += `- 可达性: ${response.status < 500 ? '✅ 正常' : '❌ 异常'}\n\n`;
            } catch (error) {
                result += `❌ 基本连接测试失败: ${error.message}\n\n`;
            }

            log('networkDiag', result, 'info');
        }

        async function browserDiagnosis() {
            addLog('networkDiag', '正在进行浏览器诊断...', 'warning');
            
            let result = '=== 浏览器诊断 ===\n\n';
            
            result += `浏览器信息:\n`;
            result += `- 用户代理: ${navigator.userAgent}\n`;
            result += `- 语言: ${navigator.language}\n`;
            result += `- 平台: ${navigator.platform}\n`;
            result += `- Cookie启用: ${navigator.cookieEnabled ? '✅ 是' : '❌ 否'}\n\n`;

            result += `支持的功能:\n`;
            result += `- Fetch API: ${typeof fetch !== 'undefined' ? '✅ 支持' : '❌ 不支持'}\n`;
            result += `- FormData: ${typeof FormData !== 'undefined' ? '✅ 支持' : '❌ 不支持'}\n`;
            result += `- File API: ${typeof File !== 'undefined' ? '✅ 支持' : '❌ 不支持'}\n`;
            result += `- LocalStorage: ${typeof localStorage !== 'undefined' ? '✅ 支持' : '❌ 不支持'}\n\n`;

            result += `控制台错误检查:\n`;
            const errors = window.console.error.toString();
            result += `- 控制台状态: ${errors.includes('native') ? '✅ 正常' : '⚠️ 可能有问题'}\n\n`;

            addLog('networkDiag', result, 'info');
        }

        // 页面加载时自动检查系统状态
        window.onload = function() {
            setTimeout(() => {
                checkSystemStatus();
            }, 1000);
        };
    </script>
</body>
</html>
