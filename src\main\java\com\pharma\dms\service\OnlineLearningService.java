package com.pharma.dms.service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pharma.dms.entity.AuditLog;
import com.pharma.dms.entity.TrainingCourse;
import com.pharma.dms.entity.TrainingProgress;
import com.pharma.dms.entity.TrainingRecord;
import com.pharma.dms.entity.User;
import com.pharma.dms.exception.BusinessException;
import com.pharma.dms.repository.TrainingCourseRepository;
import com.pharma.dms.repository.TrainingProgressRepository;
import com.pharma.dms.repository.TrainingRecordRepository;
import com.pharma.dms.repository.UserRepository;

/**
 * 在线学习服务
 * 负责学习会话管理、进度跟踪、断点续学等业务逻辑
 */
@Service
@Transactional
public class OnlineLearningService {

    @Autowired
    private TrainingProgressRepository progressRepository;

    @Autowired
    private TrainingRecordRepository recordRepository;

    @Autowired
    private TrainingCourseRepository courseRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AuditService auditService;

    // ========== 学习会话管理 ==========

    /**
     * 开始学习会话
     */
    public Map<String, Object> startLearningSession(Long courseId, Long userId, Map<String, Object> deviceInfo) {
        System.out.println("=== 开始学习会话 ===");
        System.out.println("课程ID: " + courseId + ", 用户ID: " + userId);

        // 验证课程和用户
        TrainingCourse course = courseRepository.findById(courseId)
                .orElseThrow(() -> new BusinessException("课程不存在"));
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 检查课程状态
        if (course.getStatus() != TrainingCourse.CourseStatus.ACTIVE) {
            throw new BusinessException("课程未激活，无法学习");
        }

        // 获取或创建培训记录
        TrainingRecord record = recordRepository.findByCourseAndUser(course, user)
                .orElseGet(() -> {
                    TrainingRecord newRecord = new TrainingRecord(course, user);
                    return recordRepository.save(newRecord);
                });

        // 获取或创建学习进度
        TrainingProgress progress = progressRepository.findByTrainingRecord(record)
                .orElseGet(() -> {
                    TrainingProgress newProgress = new TrainingProgress();
                    newProgress.setTrainingRecord(record);
                    newProgress.setProgressPercentage(0);
                    newProgress.setTimeSpentMinutes(0);
                    newProgress.setIsCompleted(false);
                    return progressRepository.save(newProgress);
                });

        // 生成会话ID
        String sessionId = generateSessionId();
        
        // 更新进度记录
        progress.setSessionId(sessionId);
        progress.setStartTime(LocalDateTime.now());
        progress.setLastAccessed(LocalDateTime.now());
        
        // 设置设备信息
        if (deviceInfo != null) {
            progress.setDeviceType((String) deviceInfo.get("deviceType"));
            progress.setBrowserInfo((String) deviceInfo.get("browserInfo"));
            progress.setIpAddress((String) deviceInfo.get("ipAddress"));
            Object isMobile = deviceInfo.get("isMobileDevice");
            if (isMobile != null) {
                progress.setIsMobileDevice(Boolean.parseBoolean(isMobile.toString()));
            }
        }

        progressRepository.save(progress);

        // 记录审计日志
        auditService.logSystemEvent("LEARNING_SESSION_STARTED",
                String.format("用户 %s 开始学习课程 %s", user.getUsername(), course.getTitle()),
                AuditLog.Severity.INFO);

        // 构建响应数据
        Map<String, Object> response = new HashMap<>();
        response.put("sessionId", sessionId);
        response.put("courseId", courseId);
        response.put("courseTitle", course.getTitle());
        response.put("currentProgress", progress.getProgressPercentage());
        response.put("timeSpent", progress.getTimeSpentMinutes());
        response.put("bookmarkPosition", progress.getBookmarkPosition());
        response.put("canResume", progress.getProgressPercentage() > 0);
        response.put("estimatedDuration", course.getDurationMinutes());
        response.put("contentType", course.getContentType().name());
        
        // 添加内容信息
        Map<String, Object> contentInfo = new HashMap<>();
        contentInfo.put("videoUrl", course.getVideoUrl());
        contentInfo.put("documentPath", course.getDocumentPath());
        contentInfo.put("externalLink", course.getExternalLink());
        response.put("contentInfo", contentInfo);

        System.out.println("✅ 学习会话开始成功，会话ID: " + sessionId);
        return response;
    }

    /**
     * 更新学习进度
     */
    public void updateLearningProgress(String sessionId, Integer progressPercentage, 
                                     Integer timeSpentMinutes, String bookmarkPosition) {
        System.out.println("=== 更新学习进度 ===");
        System.out.println("会话ID: " + sessionId + ", 进度: " + progressPercentage + "%");

        TrainingProgress progress = progressRepository.findBySessionId(sessionId)
                .orElseThrow(() -> new BusinessException("学习会话不存在"));

        // 更新进度信息
        if (progressPercentage != null && progressPercentage >= 0 && progressPercentage <= 100) {
            progress.setProgressPercentage(progressPercentage);
        }

        if (timeSpentMinutes != null && timeSpentMinutes >= 0) {
            progress.setTimeSpentMinutes(timeSpentMinutes);
        }

        if (bookmarkPosition != null) {
            progress.setBookmarkPosition(bookmarkPosition);
            progress.setLastBookmarkTime(LocalDateTime.now());
        }

        progress.setLastAccessed(LocalDateTime.now());
        progress.setAutoSaved(true);

        // 检查是否完成
        if (progressPercentage != null && progressPercentage >= 100 && !progress.getIsCompleted()) {
            progress.setIsCompleted(true);
            progress.setCompletionDate(LocalDateTime.now());
            
            // 更新培训记录状态
            TrainingRecord record = progress.getTrainingRecord();
            if (record.getStatus() != TrainingRecord.TrainingStatus.COMPLETED) {
                record.setStatus(TrainingRecord.TrainingStatus.COMPLETED);
                record.setCompletionDate(LocalDateTime.now());
                recordRepository.save(record);
            }

            // 记录完成事件
            auditService.logSystemEvent("LEARNING_COMPLETED",
                    String.format("用户 %s 完成课程 %s 学习", 
                        record.getUser().getUsername(), 
                        record.getCourse().getTitle()),
                    AuditLog.Severity.INFO);
        }

        progressRepository.save(progress);
        System.out.println("✅ 学习进度更新成功");
    }

    /**
     * 保存学习书签
     */
    public void saveBookmark(String sessionId, String position, String notes) {
        TrainingProgress progress = progressRepository.findBySessionId(sessionId)
                .orElseThrow(() -> new BusinessException("学习会话不存在"));

        progress.setBookmarkPosition(position);
        progress.setLastBookmarkTime(LocalDateTime.now());
        
        if (notes != null && !notes.trim().isEmpty()) {
            String existingNotes = progress.getNotes();
            String newNotes = existingNotes != null ? existingNotes + "\n" + notes : notes;
            progress.setNotes(newNotes);
        }

        progressRepository.save(progress);
    }

    /**
     * 结束学习会话
     */
    public Map<String, Object> endLearningSession(String sessionId, Integer finalProgress, 
                                                 Integer totalTimeSpent, String finalPosition) {
        System.out.println("=== 结束学习会话 ===");
        System.out.println("会话ID: " + sessionId);

        TrainingProgress progress = progressRepository.findBySessionId(sessionId)
                .orElseThrow(() -> new BusinessException("学习会话不存在"));

        // 更新最终进度
        if (finalProgress != null) {
            progress.setProgressPercentage(finalProgress);
        }
        
        if (totalTimeSpent != null) {
            progress.setTimeSpentMinutes(totalTimeSpent);
        }
        
        if (finalPosition != null) {
            progress.setBookmarkPosition(finalPosition);
        }

        progress.setEndTime(LocalDateTime.now());
        progress.setLastAccessed(LocalDateTime.now());

        // 计算本次学习时长
        if (progress.getStartTime() != null && progress.getEndTime() != null) {
            long sessionMinutes = java.time.Duration.between(progress.getStartTime(), progress.getEndTime()).toMinutes();
            progress.setTimeSpentMinutes(progress.getTimeSpentMinutes() + (int) sessionMinutes);
        }

        progressRepository.save(progress);

        // 记录审计日志
        auditService.logSystemEvent("LEARNING_SESSION_ENDED",
                String.format("用户 %s 结束学习会话，进度: %d%%", 
                    progress.getTrainingRecord().getUser().getUsername(), 
                    progress.getProgressPercentage()),
                AuditLog.Severity.INFO);

        // 构建响应数据
        Map<String, Object> response = new HashMap<>();
        response.put("finalProgress", progress.getProgressPercentage());
        response.put("totalTimeSpent", progress.getTimeSpentMinutes());
        response.put("isCompleted", progress.getIsCompleted());
        response.put("sessionDuration", progress.getEndTime() != null && progress.getStartTime() != null ? 
            java.time.Duration.between(progress.getStartTime(), progress.getEndTime()).toMinutes() : 0);

        System.out.println("✅ 学习会话结束成功");
        return response;
    }

    /**
     * 获取学习进度
     */
    public Map<String, Object> getLearningProgress(Long courseId, Long userId) {
        TrainingCourse course = courseRepository.findById(courseId)
                .orElseThrow(() -> new BusinessException("课程不存在"));
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        Optional<TrainingRecord> recordOpt = recordRepository.findByCourseAndUser(course, user);
        if (recordOpt.isEmpty()) {
            // 返回初始状态
            Map<String, Object> response = new HashMap<>();
            response.put("progressPercentage", 0);
            response.put("timeSpentMinutes", 0);
            response.put("isCompleted", false);
            response.put("canResume", false);
            return response;
        }

        TrainingRecord record = recordOpt.get();
        Optional<TrainingProgress> progressOpt = progressRepository.findByTrainingRecord(record);
        
        if (progressOpt.isEmpty()) {
            // 返回初始状态
            Map<String, Object> response = new HashMap<>();
            response.put("progressPercentage", 0);
            response.put("timeSpentMinutes", 0);
            response.put("isCompleted", false);
            response.put("canResume", false);
            return response;
        }

        TrainingProgress progress = progressOpt.get();
        Map<String, Object> response = new HashMap<>();
        response.put("progressPercentage", progress.getProgressPercentage());
        response.put("timeSpentMinutes", progress.getTimeSpentMinutes());
        response.put("isCompleted", progress.getIsCompleted());
        response.put("canResume", progress.getProgressPercentage() > 0);
        response.put("bookmarkPosition", progress.getBookmarkPosition());
        response.put("lastAccessed", progress.getLastAccessed());
        response.put("completionDate", progress.getCompletionDate());

        return response;
    }

    /**
     * 心跳检测 - 保持会话活跃
     */
    public void heartbeat(String sessionId) {
        TrainingProgress progress = progressRepository.findBySessionId(sessionId)
                .orElseThrow(() -> new BusinessException("学习会话不存在"));

        progress.setLastAccessed(LocalDateTime.now());
        progressRepository.save(progress);
    }

    /**
     * 增加交互计数
     */
    public void recordInteraction(String sessionId, String interactionType) {
        TrainingProgress progress = progressRepository.findBySessionId(sessionId)
                .orElseThrow(() -> new BusinessException("学习会话不存在"));

        progress.setInteractionCount(progress.getInteractionCount() + 1);
        progress.setLastAccessed(LocalDateTime.now());
        progressRepository.save(progress);
    }

    // ========== 工具方法 ==========

    private String generateSessionId() {
        return "LEARN_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 10000);
    }

    /**
     * 计算学习效率
     */
    public Double calculateLearningEfficiency(Long courseId, Long userId) {
        TrainingCourse course = courseRepository.findById(courseId)
                .orElseThrow(() -> new BusinessException("课程不存在"));
        
        Map<String, Object> progress = getLearningProgress(courseId, userId);
        Integer timeSpent = (Integer) progress.get("timeSpentMinutes");
        Integer progressPercentage = (Integer) progress.get("progressPercentage");
        
        if (timeSpent == null || timeSpent == 0 || progressPercentage == null || progressPercentage == 0) {
            return 0.0;
        }
        
        // 效率 = 进度百分比 / 实际用时 * 预估用时
        Integer estimatedDuration = course.getDurationMinutes();
        if (estimatedDuration == null || estimatedDuration == 0) {
            estimatedDuration = 60; // 默认1小时
        }
        
        return (double) progressPercentage / timeSpent * estimatedDuration;
    }

    /**
     * 获取学习统计
     */
    public Map<String, Object> getLearningStatistics(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        List<TrainingRecord> records = recordRepository.findByUser(user);
        
        int totalCourses = records.size();
        int completedCourses = (int) records.stream()
                .filter(r -> r.getStatus() == TrainingRecord.TrainingStatus.COMPLETED)
                .count();
        int inProgressCourses = (int) records.stream()
                .filter(r -> r.getStatus() == TrainingRecord.TrainingStatus.IN_PROGRESS)
                .count();
        
        int totalTimeSpent = records.stream()
                .mapToInt(record -> {
                    Optional<TrainingProgress> progressOpt = progressRepository.findByTrainingRecord(record);
                    return progressOpt.map(TrainingProgress::getTimeSpentMinutes).orElse(0);
                })
                .sum();

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalCourses", totalCourses);
        statistics.put("completedCourses", completedCourses);
        statistics.put("inProgressCourses", inProgressCourses);
        statistics.put("completionRate", totalCourses > 0 ? (double) completedCourses / totalCourses * 100 : 0);
        statistics.put("totalTimeSpentMinutes", totalTimeSpent);
        statistics.put("totalTimeSpentHours", totalTimeSpent / 60.0);

        return statistics;
    }
}
