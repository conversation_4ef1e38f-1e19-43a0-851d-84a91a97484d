<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档上传诊断工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; white-space: pre-wrap; font-family: monospace; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
        .warning { background: #fff3e0; color: #f57c00; }
        .info { background: #e3f2fd; color: #1976d2; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; border: none; }
        .btn-success { background: #28a745; color: white; border: none; }
        .btn-warning { background: #ffc107; color: black; border: none; }
        .btn-danger { background: #dc3545; color: white; border: none; }
        input[type="file"] { margin: 10px 0; padding: 5px; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <h1>🔍 DMS文档上传诊断工具</h1>
    
    <div class="section">
        <h2>快速登录和上传测试</h2>
        <button class="btn-primary" onclick="quickLogin()">快速登录</button>
        <input type="file" id="quickFile" accept=".txt,.pdf,.doc,.docx">
        <button class="btn-success" onclick="quickUpload()">快速上传测试</button>
        <div id="quickResult" class="result"></div>
    </div>

    <div class="section">
        <h2>详细诊断</h2>
        <button class="btn-warning" onclick="fullDiagnosis()">完整诊断</button>
        <div id="diagResult" class="result"></div>
    </div>

    <script>
        let authToken = null;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = 'result ' + type;
            console.log(message);
        }

        function addLog(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.innerHTML += `[${timestamp}] ${message}\n`;
            console.log(message);
        }

        async function quickLogin() {
            try {
                log('quickResult', '正在登录...', 'warning');
                
                const response = await fetch('/dms/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    authToken = result.data.token;
                    localStorage.setItem('token', authToken);
                    log('quickResult', `✅ 登录成功！用户: ${result.data.username}`, 'success');
                } else {
                    const error = await response.text();
                    log('quickResult', `❌ 登录失败: ${error}`, 'error');
                }
            } catch (error) {
                log('quickResult', `❌ 登录异常: ${error.message}`, 'error');
            }
        }

        async function quickUpload() {
            if (!authToken) {
                log('quickResult', '❌ 请先登录', 'error');
                return;
            }

            const fileInput = document.getElementById('quickFile');
            if (!fileInput.files[0]) {
                log('quickResult', '❌ 请选择文件', 'error');
                return;
            }

            try {
                log('quickResult', '正在上传文档...', 'warning');
                
                const file = fileInput.files[0];
                const formData = new FormData();
                formData.append('file', file);
                formData.append('title', '快速测试文档 - ' + new Date().toLocaleTimeString());
                formData.append('description', '这是一个快速上传测试');

                console.log('=== 上传详情 ===');
                console.log('文件:', file.name, file.size, file.type);
                console.log('Token长度:', authToken.length);

                const response = await fetch('/dms/api/documents/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    },
                    body: formData
                });

                console.log('响应状态:', response.status);
                console.log('响应头:', [...response.headers.entries()]);

                if (response.ok) {
                    const result = await response.json();
                    log('quickResult', 
                        `✅ 上传成功！\n` +
                        `文档ID: ${result.data.id}\n` +
                        `标题: ${result.data.title}\n` +
                        `文件名: ${result.data.originalFileName}\n` +
                        `大小: ${result.data.fileSize} 字节`, 
                        'success'
                    );
                } else {
                    const errorText = await response.text();
                    console.error('上传失败:', errorText);
                    log('quickResult', `❌ 上传失败 (${response.status}): ${errorText}`, 'error');
                }
            } catch (error) {
                console.error('上传异常:', error);
                log('quickResult', `❌ 上传异常: ${error.message}`, 'error');
            }
        }

        async function fullDiagnosis() {
            log('diagResult', '开始完整诊断...', 'warning');
            
            let result = '=== 完整系统诊断 ===\n\n';
            
            // 1. 检查基本环境
            result += '1. 基本环境检查:\n';
            result += `- 浏览器: ${navigator.userAgent.split(' ')[0]}\n`;
            result += `- 当前URL: ${window.location.href}\n`;
            result += `- 网络状态: ${navigator.onLine ? '在线' : '离线'}\n`;
            result += `- LocalStorage: ${typeof localStorage !== 'undefined' ? '支持' : '不支持'}\n\n`;

            // 2. 检查认证状态
            result += '2. 认证状态检查:\n';
            const token = localStorage.getItem('token');
            result += `- Token存在: ${token ? '是' : '否'}\n`;
            if (token) {
                result += `- Token长度: ${token.length}\n`;
                result += `- Token格式: ${token.startsWith('eyJ') ? 'JWT' : '其他'}\n`;
                authToken = token;
            }
            result += '\n';

            // 3. 测试API连接
            result += '3. API连接测试:\n';
            try {
                const apiResponse = await fetch('/dms/api/documents?page=0&size=1', {
                    method: 'GET',
                    headers: authToken ? { 'Authorization': 'Bearer ' + authToken } : {}
                });
                result += `- 文档API状态: ${apiResponse.status}\n`;
                result += `- API可访问: ${apiResponse.status < 500 ? '是' : '否'}\n`;
            } catch (error) {
                result += `- API连接失败: ${error.message}\n`;
            }
            result += '\n';

            // 4. 检查上传端点
            result += '4. 上传端点检查:\n';
            try {
                const uploadResponse = await fetch('/dms/api/documents/upload', {
                    method: 'OPTIONS',
                    headers: authToken ? { 'Authorization': 'Bearer ' + authToken } : {}
                });
                result += `- 上传端点状态: ${uploadResponse.status}\n`;
                result += `- 允许的方法: ${uploadResponse.headers.get('Allow') || '未指定'}\n`;
            } catch (error) {
                result += `- 上传端点检查失败: ${error.message}\n`;
            }
            result += '\n';

            // 5. 文件API支持检查
            result += '5. 文件API支持:\n';
            result += `- File API: ${typeof File !== 'undefined' ? '支持' : '不支持'}\n`;
            result += `- FormData: ${typeof FormData !== 'undefined' ? '支持' : '不支持'}\n`;
            result += `- Fetch API: ${typeof fetch !== 'undefined' ? '支持' : '不支持'}\n\n`;

            // 6. 建议
            result += '6. 诊断建议:\n';
            if (!authToken) {
                result += '- ⚠️ 请先登录系统\n';
            }
            if (navigator.onLine) {
                result += '- ✅ 网络连接正常\n';
            } else {
                result += '- ❌ 网络连接异常\n';
            }
            result += '- 💡 如果上传仍然失败，请检查服务器日志\n';
            result += '- 💡 确保文件大小不超过50MB\n';
            result += '- 💡 确保文件类型被允许\n';

            log('diagResult', result, 'info');
        }

        // 页面加载时自动检查token
        window.onload = function() {
            const savedToken = localStorage.getItem('token');
            if (savedToken) {
                authToken = savedToken;
                log('quickResult', '✅ 已从本地存储加载Token', 'success');
            }
        };
    </script>
</body>
</html>
