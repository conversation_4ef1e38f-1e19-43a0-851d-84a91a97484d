<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档上传测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .result { margin: 15px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .progress { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; display: none; }
        .progress-bar { height: 100%; background: #007bff; transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 文档上传测试</h1>
        
        <div class="form-group">
            <button onclick="quickLogin()">快速登录</button>
            <span id="loginStatus">未登录</span>
        </div>

        <form id="uploadForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">选择文件 *</label>
                <input type="file" id="file" name="file" required>
                <small>支持：PDF, Word, Excel, 文本, 图片文件，最大50MB</small>
            </div>

            <div class="form-group">
                <label for="title">文档标题 *</label>
                <input type="text" id="title" name="title" required placeholder="请输入文档标题">
            </div>

            <div class="form-group">
                <label for="description">文档描述</label>
                <textarea id="description" name="description" rows="3" placeholder="请输入文档描述（可选）"></textarea>
            </div>

            <div class="form-group">
                <label for="categoryId">文档分类</label>
                <select id="categoryId" name="categoryId">
                    <option value="">选择分类（可选）</option>
                </select>
            </div>

            <div class="progress" id="uploadProgress">
                <div class="progress-bar" id="progressBar" style="width: 0%"></div>
            </div>

            <div class="form-group">
                <button type="submit" id="uploadBtn">上传文档</button>
                <button type="button" onclick="resetForm()">重置</button>
            </div>
        </form>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        let authToken = null;
        let isUploading = false;

        // 快速登录
        async function quickLogin() {
            try {
                const response = await fetch('/dms/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    authToken = result.data.token;
                    document.getElementById('loginStatus').textContent = '已登录 (admin)';
                    document.getElementById('loginStatus').style.color = 'green';
                    showResult('登录成功！', 'success');
                    loadCategories();
                } else {
                    throw new Error('登录失败');
                }
            } catch (error) {
                showResult('登录失败: ' + error.message, 'error');
            }
        }

        // 加载分类
        async function loadCategories() {
            try {
                const response = await fetch('/dms/api/documents/categories', {
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const select = document.getElementById('categoryId');
                    select.innerHTML = '<option value="">选择分类（可选）</option>';
                    
                    if (result.data && Array.isArray(result.data)) {
                        result.data.forEach(category => {
                            const option = document.createElement('option');
                            option.value = category.id;
                            option.textContent = category.name;
                            select.appendChild(option);
                        });
                    }
                }
            } catch (error) {
                console.error('加载分类失败:', error);
            }
        }

        // 表单提交处理
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (!authToken) {
                showResult('请先登录', 'warning');
                return;
            }

            if (isUploading) {
                showResult('正在上传中，请稍候...', 'warning');
                return;
            }

            const fileInput = document.getElementById('file');
            const titleInput = document.getElementById('title');

            if (!fileInput.files[0]) {
                showResult('请选择要上传的文件', 'warning');
                return;
            }

            if (!titleInput.value.trim()) {
                showResult('请填写文档标题', 'warning');
                titleInput.focus();
                return;
            }

            const file = fileInput.files[0];
            if (file.size > 50 * 1024 * 1024) {
                showResult('文件大小不能超过50MB', 'error');
                return;
            }

            try {
                isUploading = true;
                updateUploadButton(true);
                showProgress(true);

                const formData = new FormData(this);
                
                console.log('开始上传文档...');
                console.log('文件:', file.name, file.size, file.type);
                console.log('标题:', titleInput.value);

                const response = await fetch('/dms/api/documents/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    },
                    body: formData
                });

                console.log('响应状态:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('上传成功:', result);
                    showResult('文档上传成功！', 'success');
                    resetForm();
                } else {
                    const errorText = await response.text();
                    console.error('上传失败:', errorText);
                    throw new Error(errorText || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('上传异常:', error);
                showResult('上传失败: ' + error.message, 'error');
            } finally {
                isUploading = false;
                updateUploadButton(false);
                showProgress(false);
            }
        });

        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
            
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 5000);
        }

        // 更新上传按钮状态
        function updateUploadButton(uploading) {
            const btn = document.getElementById('uploadBtn');
            if (uploading) {
                btn.disabled = true;
                btn.textContent = '上传中...';
            } else {
                btn.disabled = false;
                btn.textContent = '上传文档';
            }
        }

        // 显示进度条
        function showProgress(show) {
            const progress = document.getElementById('uploadProgress');
            if (show) {
                progress.style.display = 'block';
                // 模拟进度
                let width = 0;
                const interval = setInterval(() => {
                    width += 10;
                    document.getElementById('progressBar').style.width = width + '%';
                    if (width >= 90) {
                        clearInterval(interval);
                    }
                }, 200);
            } else {
                progress.style.display = 'none';
                document.getElementById('progressBar').style.width = '0%';
            }
        }

        // 重置表单
        function resetForm() {
            document.getElementById('uploadForm').reset();
            showProgress(false);
        }

        // 页面加载时自动登录
        window.addEventListener('load', () => {
            console.log('页面加载完成，准备测试上传功能');
        });
    </script>
</body>
</html>
