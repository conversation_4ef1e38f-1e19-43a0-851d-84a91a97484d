/**
 * 统一的前端日志工具
 * 提供一致的日志格式和级别管理
 */
class Logger {
    constructor(moduleName) {
        this.moduleName = moduleName;
        this.logLevel = this.getLogLevel();
        this.logHistory = [];
        this.maxHistorySize = 1000;
    }

    // 获取日志级别配置
    getLogLevel() {
        const level = localStorage.getItem('logLevel') || 'INFO';
        const levels = { 'DEBUG': 0, 'INFO': 1, 'WARN': 2, 'ERROR': 3 };
        return levels[level] || 1;
    }

    // 格式化日志消息
    formatMessage(level, message, data = null) {
        const timestamp = new Date().toISOString();
        const formattedMessage = `[${timestamp}] [${level}] [${this.moduleName}] ${message}`;
        
        if (data) {
            return { message: formattedMessage, data };
        }
        return formattedMessage;
    }

    // 记录日志到历史
    recordLog(level, message, data) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            level,
            module: this.moduleName,
            message,
            data
        };

        this.logHistory.push(logEntry);
        
        // 限制历史记录大小
        if (this.logHistory.length > this.maxHistorySize) {
            this.logHistory.shift();
        }

        // 发送到服务器（如果需要）
        this.sendToServer(logEntry);
    }

    // 发送日志到服务器
    async sendToServer(logEntry) {
        // 只发送ERROR级别的日志到服务器
        if (logEntry.level === 'ERROR' && window.authUtils) {
            try {
                await authUtils.secureApiCall('/dms/api/logs/frontend', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(logEntry)
                });
            } catch (error) {
                // 避免日志发送失败导致的循环错误
                console.error('Failed to send log to server:', error);
            }
        }
    }

    // DEBUG级别日志
    debug(message, data = null) {
        if (this.logLevel <= 0) {
            const formatted = this.formatMessage('DEBUG', message, data);
            console.debug(formatted.message || formatted, formatted.data);
            this.recordLog('DEBUG', message, data);
        }
    }

    // INFO级别日志
    info(message, data = null) {
        if (this.logLevel <= 1) {
            const formatted = this.formatMessage('INFO', message, data);
            console.info(formatted.message || formatted, formatted.data);
            this.recordLog('INFO', message, data);
        }
    }

    // WARN级别日志
    warn(message, data = null) {
        if (this.logLevel <= 2) {
            const formatted = this.formatMessage('WARN', message, data);
            console.warn(formatted.message || formatted, formatted.data);
            this.recordLog('WARN', message, data);
        }
    }

    // ERROR级别日志
    error(message, error = null) {
        if (this.logLevel <= 3) {
            const errorData = error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
            } : null;
            
            const formatted = this.formatMessage('ERROR', message, errorData);
            console.error(formatted.message || formatted, formatted.data);
            this.recordLog('ERROR', message, errorData);
        }
    }

    // 性能日志
    performance(operation, startTime, endTime = Date.now()) {
        const duration = endTime - startTime;
        const message = `Performance: ${operation} took ${duration}ms`;
        
        if (duration > 1000) {
            this.warn(message, { operation, duration });
        } else {
            this.debug(message, { operation, duration });
        }
    }

    // 用户操作日志
    userAction(action, details = null) {
        this.info(`User Action: ${action}`, details);
    }

    // API调用日志
    apiCall(method, url, status, duration) {
        const message = `API Call: ${method} ${url} - ${status} (${duration}ms)`;
        
        if (status >= 400) {
            this.error(message, { method, url, status, duration });
        } else if (duration > 2000) {
            this.warn(message, { method, url, status, duration });
        } else {
            this.debug(message, { method, url, status, duration });
        }
    }

    // 获取日志历史
    getHistory(level = null, limit = 100) {
        let logs = this.logHistory;
        
        if (level) {
            logs = logs.filter(log => log.level === level);
        }
        
        return logs.slice(-limit);
    }

    // 清除日志历史
    clearHistory() {
        this.logHistory = [];
    }

    // 导出日志
    exportLogs() {
        const logs = this.getHistory();
        const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${this.moduleName}-logs-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 创建全局日志实例
window.Logger = Logger;

// 为不同模块创建日志实例
window.loggers = {
    document: new Logger('DocumentManager'),
    auth: new Logger('Authentication'),
    ui: new Logger('UIEnhancements'),
    upload: new Logger('UploadDebugger'),
    system: new Logger('SystemOverview')
};

// 全局错误处理
window.addEventListener('error', (event) => {
    window.loggers.system.error('Global Error', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    window.loggers.system.error('Unhandled Promise Rejection', event.reason);
});

console.log('✅ 统一日志系统已初始化');
