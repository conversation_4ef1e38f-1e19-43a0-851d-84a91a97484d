package com.pharma.dms.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 学习会话实体
 * 记录用户的每次学习会话详情
 */
@Entity
@Table(name = "learning_sessions")
public class LearningSession extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "training_record_id", nullable = false)
    private TrainingRecord trainingRecord;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(name = "session_id", nullable = false, unique = true)
    private String sessionId;

    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private SessionStatus status = SessionStatus.ACTIVE;

    @Column(name = "total_duration_minutes")
    private Integer totalDurationMinutes = 0;

    @Column(name = "active_duration_minutes")
    private Integer activeDurationMinutes = 0;

    @Column(name = "pause_duration_minutes")
    private Integer pauseDurationMinutes = 0;

    @Column(name = "pause_count")
    private Integer pauseCount = 0;

    @Column(name = "interaction_count")
    private Integer interactionCount = 0;

    @Column(name = "page_views")
    private Integer pageViews = 0;

    @Column(name = "video_watch_time_seconds")
    private Integer videoWatchTimeSeconds = 0;

    @Column(name = "document_read_time_seconds")
    private Integer documentReadTimeSeconds = 0;

    @Column(name = "quiz_attempts")
    private Integer quizAttempts = 0;

    @Column(name = "current_module_id")
    private Long currentModuleId;

    @Column(name = "current_position")
    private String currentPosition; // JSON格式的位置信息

    @Column(name = "device_type")
    private String deviceType;

    @Column(name = "browser_info")
    private String browserInfo;

    @Column(name = "ip_address")
    private String ipAddress;

    @Column(name = "user_agent")
    private String userAgent;

    @Column(name = "screen_resolution")
    private String screenResolution;

    @Column(name = "is_mobile_device")
    private Boolean isMobileDevice = false;

    @Column(name = "connection_quality")
    private String connectionQuality; // GOOD, FAIR, POOR

    @Column(name = "last_heartbeat")
    private LocalDateTime lastHeartbeat;

    @Column(name = "completion_percentage")
    private Integer completionPercentage = 0;

    @Column(name = "notes")
    private String notes;

    @OneToMany(mappedBy = "learningSession", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<SessionActivity> activities = new ArrayList<>();

    // Constructors
    public LearningSession() {
        this.startTime = LocalDateTime.now();
        this.lastHeartbeat = LocalDateTime.now();
        this.sessionId = generateSessionId();
    }

    public LearningSession(TrainingRecord trainingRecord, User user) {
        this();
        this.trainingRecord = trainingRecord;
        this.user = user;
    }

    // Getters and Setters
    public TrainingRecord getTrainingRecord() {
        return trainingRecord;
    }

    public void setTrainingRecord(TrainingRecord trainingRecord) {
        this.trainingRecord = trainingRecord;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public SessionStatus getStatus() {
        return status;
    }

    public void setStatus(SessionStatus status) {
        this.status = status;
    }

    public Integer getTotalDurationMinutes() {
        return totalDurationMinutes;
    }

    public void setTotalDurationMinutes(Integer totalDurationMinutes) {
        this.totalDurationMinutes = totalDurationMinutes;
    }

    public Integer getActiveDurationMinutes() {
        return activeDurationMinutes;
    }

    public void setActiveDurationMinutes(Integer activeDurationMinutes) {
        this.activeDurationMinutes = activeDurationMinutes;
    }

    public Integer getPauseDurationMinutes() {
        return pauseDurationMinutes;
    }

    public void setPauseDurationMinutes(Integer pauseDurationMinutes) {
        this.pauseDurationMinutes = pauseDurationMinutes;
    }

    public Integer getPauseCount() {
        return pauseCount;
    }

    public void setPauseCount(Integer pauseCount) {
        this.pauseCount = pauseCount;
    }

    public Integer getInteractionCount() {
        return interactionCount;
    }

    public void setInteractionCount(Integer interactionCount) {
        this.interactionCount = interactionCount;
    }

    public Integer getPageViews() {
        return pageViews;
    }

    public void setPageViews(Integer pageViews) {
        this.pageViews = pageViews;
    }

    public Integer getVideoWatchTimeSeconds() {
        return videoWatchTimeSeconds;
    }

    public void setVideoWatchTimeSeconds(Integer videoWatchTimeSeconds) {
        this.videoWatchTimeSeconds = videoWatchTimeSeconds;
    }

    public Integer getDocumentReadTimeSeconds() {
        return documentReadTimeSeconds;
    }

    public void setDocumentReadTimeSeconds(Integer documentReadTimeSeconds) {
        this.documentReadTimeSeconds = documentReadTimeSeconds;
    }

    public Integer getQuizAttempts() {
        return quizAttempts;
    }

    public void setQuizAttempts(Integer quizAttempts) {
        this.quizAttempts = quizAttempts;
    }

    public Long getCurrentModuleId() {
        return currentModuleId;
    }

    public void setCurrentModuleId(Long currentModuleId) {
        this.currentModuleId = currentModuleId;
    }

    public String getCurrentPosition() {
        return currentPosition;
    }

    public void setCurrentPosition(String currentPosition) {
        this.currentPosition = currentPosition;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getBrowserInfo() {
        return browserInfo;
    }

    public void setBrowserInfo(String browserInfo) {
        this.browserInfo = browserInfo;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getScreenResolution() {
        return screenResolution;
    }

    public void setScreenResolution(String screenResolution) {
        this.screenResolution = screenResolution;
    }

    public Boolean getIsMobileDevice() {
        return isMobileDevice;
    }

    public void setIsMobileDevice(Boolean isMobileDevice) {
        this.isMobileDevice = isMobileDevice;
    }

    public String getConnectionQuality() {
        return connectionQuality;
    }

    public void setConnectionQuality(String connectionQuality) {
        this.connectionQuality = connectionQuality;
    }

    public LocalDateTime getLastHeartbeat() {
        return lastHeartbeat;
    }

    public void setLastHeartbeat(LocalDateTime lastHeartbeat) {
        this.lastHeartbeat = lastHeartbeat;
    }

    public Integer getCompletionPercentage() {
        return completionPercentage;
    }

    public void setCompletionPercentage(Integer completionPercentage) {
        this.completionPercentage = completionPercentage;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<SessionActivity> getActivities() {
        return activities;
    }

    public void setActivities(List<SessionActivity> activities) {
        this.activities = activities;
    }

    // Helper methods
    public void updateHeartbeat() {
        this.lastHeartbeat = LocalDateTime.now();
    }

    public void endSession() {
        this.endTime = LocalDateTime.now();
        this.status = SessionStatus.COMPLETED;
        
        if (this.startTime != null && this.endTime != null) {
            long minutes = java.time.Duration.between(this.startTime, this.endTime).toMinutes();
            this.totalDurationMinutes = (int) minutes;
        }
    }

    public void pauseSession() {
        this.status = SessionStatus.PAUSED;
        this.pauseCount++;
    }

    public void resumeSession() {
        this.status = SessionStatus.ACTIVE;
        this.lastHeartbeat = LocalDateTime.now();
    }

    public boolean isActive() {
        return this.status == SessionStatus.ACTIVE;
    }

    public boolean isExpired() {
        if (this.lastHeartbeat == null) return true;
        return LocalDateTime.now().minusMinutes(30).isAfter(this.lastHeartbeat);
    }

    private String generateSessionId() {
        return "SESSION_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    // Enums
    public enum SessionStatus {
        ACTIVE("活跃"),
        PAUSED("暂停"),
        COMPLETED("完成"),
        EXPIRED("过期"),
        TERMINATED("终止");

        private final String displayName;

        SessionStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
