<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人资料 - 制药文档管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" style="min-height: 100vh; background-color: #f8f9fa; border-right: 1px solid #dee2e6;">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand mb-4" style="font-weight: bold; color: #0d6efd !important;">
                        <i class="fas fa-pills me-2"></i>
                        制药文档管理系统
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/users" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/documents" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-file-alt me-2"></i>
                                文档管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/system-overview" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-line me-2"></i>
                                系统概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/my-training" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user-graduate me-2"></i>
                                我的培训
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-courses" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-graduation-cap me-2"></i>
                                培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-bar me-2"></i>
                                报表管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/profile" style="background-color: #0d6efd; color: white !important; border-radius: 0.375rem;">
                                <i class="fas fa-user me-2"></i>
                                个人资料
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">个人资料</h1>
                </div>

                <!-- Page content -->
        <div class="row">
            <div class="col-lg-8">
                <!-- Profile Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">个人信息</h6>
                    </div>
                    <div class="card-body">
                        <form id="profileForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="username" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">邮箱</label>
                                        <input type="email" class="form-control" id="email">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="firstName" class="form-label">姓</label>
                                        <input type="text" class="form-control" id="firstName">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lastName" class="form-label">名</label>
                                        <input type="text" class="form-control" id="lastName">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">电话</label>
                                        <input type="tel" class="form-control" id="phone">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="department" class="form-label">部门</label>
                                        <input type="text" class="form-control" id="department" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="roles" class="form-label">角色</label>
                                <div id="roles"></div>
                            </div>
                            <button type="submit" class="btn btn-primary">更新资料</button>
                        </form>
                    </div>
                </div>

                <!-- Change Password -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">修改密码</h6>
                    </div>
                    <div class="card-body">
                        <form id="passwordForm">
                            <div class="mb-3">
                                <label for="currentPassword" class="form-label">当前密码</label>
                                <input type="password" class="form-control" id="currentPassword" required>
                            </div>
                            <div class="mb-3">
                                <label for="newPassword" class="form-label">新密码</label>
                                <input type="password" class="form-control" id="newPassword" required>
                            </div>
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">确认新密码</label>
                                <input type="password" class="form-control" id="confirmPassword" required>
                            </div>
                            <button type="submit" class="btn btn-warning">修改密码</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Profile Summary -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">个人概要</h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-user-circle fa-5x text-gray-300"></i>
                        </div>
                        <h5 id="fullName">加载中...</h5>
                        <p class="text-muted" id="userRole">加载中...</p>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong>注册时间</strong>
                                <p id="memberSince">加载中...</p>
                            </div>
                            <div class="col-6">
                                <strong>最后登录</strong>
                                <p id="lastLogin">加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Status -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Account Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Account Status</span>
                            <span class="badge bg-success" id="accountStatus">Active</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Email Verified</span>
                            <span class="badge bg-success" id="emailVerified">Yes</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Two-Factor Auth</span>
                            <span class="badge bg-warning" id="twoFactorAuth">Disabled</span>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
                    </div>
                    <div class="card-body">
                        <div class="small text-gray-500">Today</div>
                        <div class="mb-2">
                            <i class="fas fa-sign-in-alt text-success me-2"></i>
                            Logged in at 10:30 AM
                        </div>
                        <div class="small text-gray-500">Yesterday</div>
                        <div class="mb-2">
                            <i class="fas fa-file-upload text-primary me-2"></i>
                            Uploaded document
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-edit text-info me-2"></i>
                            Updated profile
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadUserProfile();
        });

        function loadUserProfile() {
            console.log('=== 加载用户资料 ===');

            // 检查认证状态
            const token = localStorage.getItem('token');
            const userStr = localStorage.getItem('user');

            console.log('Token存在:', token ? 'YES' : 'NO');
            console.log('User数据存在:', userStr ? 'YES' : 'NO');

            if (!token) {
                console.log('❌ 没有token，跳转到登录页面');
                alert('请先登录！');
                window.location.href = '/dms/login';
                return;
            }

            // Get user data from localStorage (set during login)
            let userData = {};
            try {
                userData = JSON.parse(userStr || '{}');
                console.log('✅ 用户数据解析成功:', userData);
            } catch (e) {
                console.error('❌ 用户数据解析失败:', e);
                alert('用户数据损坏，请重新登录！');
                window.location.href = '/dms/login';
                return;
            }

            if (userData.username) {
                console.log('✅ 填充用户资料表单');
                document.getElementById('username').value = userData.username || '';
                document.getElementById('email').value = userData.email || '';
                document.getElementById('firstName').value = userData.firstName || '';
                document.getElementById('lastName').value = userData.lastName || '';
                document.getElementById('fullName').textContent = `${userData.firstName || ''} ${userData.lastName || ''}`;

                // Display roles
                const rolesDiv = document.getElementById('roles');
                if (userData.roles && userData.roles.length > 0) {
                    rolesDiv.innerHTML = userData.roles.map(role =>
                        `<span class="badge bg-primary me-1">${role.replace('ROLE_', '')}</span>`
                    ).join('');
                    document.getElementById('userRole').textContent = userData.roles[0].replace('ROLE_', '');
                } else {
                    rolesDiv.innerHTML = '<span class="badge bg-secondary">USER</span>';
                    document.getElementById('userRole').textContent = 'USER';
                }

                // Set demo data for other fields
                document.getElementById('phone').value = '+86-138-0000-0000';
                document.getElementById('department').value = '信息技术部';
                document.getElementById('memberSince').textContent = '2024年1月';
                document.getElementById('lastLogin').textContent = '今天';
            } else {
                console.log('❌ 用户名不存在，可能是数据不完整');
                // 不立即跳转，而是显示默认值
                document.getElementById('username').value = '未知用户';
                document.getElementById('fullName').textContent = '未知用户';
                document.getElementById('userRole').textContent = '用户';

                // 可以选择性地跳转到登录
                // window.location.href = '/dms/login';
            }
        }

        document.getElementById('profileForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('个人资料更新功能将在第二阶段实现');
        });

        document.getElementById('passwordForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (newPassword !== confirmPassword) {
                alert('新密码不匹配！');
                return;
            }

            if (newPassword.length < 6) {
                alert('密码长度至少需要6个字符！');
                return;
            }

            alert('密码修改功能将在第二阶段实现');
        });

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 认证工具 -->
    <script src="/dms/js/auth.js"></script>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
