<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档上传下载测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; }
        input[type="file"], input[type="text"] { margin: 5px; padding: 5px; }
    </style>
</head>
<body>
    <h1>DMS 文档上传下载测试</h1>
    
    <div class="section">
        <h2>1. 登录测试</h2>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="section">
        <h2>2. 文档上传测试</h2>
        <input type="file" id="testFile" accept=".txt,.pdf,.doc,.docx">
        <input type="text" id="testTitle" placeholder="文档标题" value="测试文档">
        <input type="text" id="testDescription" placeholder="文档描述" value="这是一个测试文档">
        <button onclick="testUpload()">上传文档</button>
        <div id="uploadResult" class="result"></div>
    </div>

    <div class="section">
        <h2>3. 文档列表测试</h2>
        <button onclick="testDocumentList()">获取文档列表</button>
        <div id="listResult" class="result"></div>
    </div>

    <div class="section">
        <h2>4. 文档下载测试</h2>
        <input type="number" id="downloadId" placeholder="文档ID" value="1">
        <button onclick="testDownload()">下载文档</button>
        <div id="downloadResult" class="result"></div>
    </div>

    <script>
        let authToken = null;

        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = 'result ' + (isError ? 'error' : 'success');
            console.log(message);
        }

        async function testLogin() {
            try {
                const response = await fetch('/dms/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    authToken = result.data.token;
                    localStorage.setItem('token', authToken);
                    log('loginResult', '✅ 登录成功！Token: ' + authToken.substring(0, 20) + '...');
                } else {
                    const error = await response.text();
                    log('loginResult', '❌ 登录失败: ' + error, true);
                }
            } catch (error) {
                log('loginResult', '❌ 登录异常: ' + error.message, true);
            }
        }

        async function testUpload() {
            if (!authToken) {
                log('uploadResult', '❌ 请先登录', true);
                return;
            }

            const fileInput = document.getElementById('testFile');
            const title = document.getElementById('testTitle').value;
            const description = document.getElementById('testDescription').value;

            if (!fileInput.files[0]) {
                log('uploadResult', '❌ 请选择文件', true);
                return;
            }

            try {
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                formData.append('title', title);
                formData.append('description', description);

                const response = await fetch('/dms/api/documents/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    },
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    log('uploadResult', '✅ 上传成功！文档ID: ' + result.data.id + ', 标题: ' + result.data.title);
                } else {
                    const error = await response.text();
                    log('uploadResult', '❌ 上传失败: ' + error, true);
                }
            } catch (error) {
                log('uploadResult', '❌ 上传异常: ' + error.message, true);
            }
        }

        async function testDocumentList() {
            if (!authToken) {
                log('listResult', '❌ 请先登录', true);
                return;
            }

            try {
                const response = await fetch('/dms/api/documents?page=0&size=5', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const docs = result.data.content;
                    let message = '✅ 获取文档列表成功！共 ' + result.data.totalElements + ' 个文档:\n';
                    docs.forEach(doc => {
                        message += `- ID: ${doc.id}, 标题: ${doc.title}, 文件名: ${doc.originalFileName}\n`;
                    });
                    log('listResult', message.replace(/\n/g, '<br>'));
                } else {
                    const error = await response.text();
                    log('listResult', '❌ 获取列表失败: ' + error, true);
                }
            } catch (error) {
                log('listResult', '❌ 获取列表异常: ' + error.message, true);
            }
        }

        async function testDownload() {
            if (!authToken) {
                log('downloadResult', '❌ 请先登录', true);
                return;
            }

            const docId = document.getElementById('downloadId').value;
            if (!docId) {
                log('downloadResult', '❌ 请输入文档ID', true);
                return;
            }

            try {
                const response = await fetch(`/dms/api/documents/${docId}/download`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let fileName = 'download';
                    
                    if (contentDisposition) {
                        const fileNameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)/);
                        if (fileNameMatch) {
                            fileName = decodeURIComponent(fileNameMatch[1]);
                        }
                    }

                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    log('downloadResult', '✅ 下载成功！文件名: ' + fileName);
                } else {
                    const error = await response.text();
                    log('downloadResult', '❌ 下载失败: ' + error, true);
                }
            } catch (error) {
                log('downloadResult', '❌ 下载异常: ' + error.message, true);
            }
        }

        // 页面加载时尝试从localStorage获取token
        window.onload = function() {
            const savedToken = localStorage.getItem('token');
            if (savedToken) {
                authToken = savedToken;
                log('loginResult', '✅ 已从本地存储加载Token');
            }
        };
    </script>
</body>
</html>
