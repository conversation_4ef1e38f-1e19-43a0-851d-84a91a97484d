package com.pharma.dms.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.dto.PasswordChangeRequest;
import com.pharma.dms.dto.SignupRequest;
import com.pharma.dms.dto.UserUpdateRequest;
import com.pharma.dms.entity.User;
import com.pharma.dms.repository.UserRepository;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.UserService;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/users")
@CrossOrigin(originPatterns = {"http://localhost:*", "http://127.0.0.1:*", "http://**************:*"}, maxAge = 3600, allowCredentials = "true")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @GetMapping("/me")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCurrentUser(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 获取当前用户信息 ===");

            if (userPrincipal == null) {
                System.out.println("UserPrincipal为null");
                return ResponseEntity.status(400)
                        .body(ApiResponse.error("User principal is null", "Authentication failed"));
            }

            System.out.println("用户: " + userPrincipal.getUsername());

            // 直接使用userRepository而不是userService
            User user = userRepository.findByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            System.out.println("用户找到: " + user.getUsername());

            // 创建简化的用户信息响应
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("email", user.getEmail());
            userInfo.put("firstName", user.getFirstName());
            userInfo.put("lastName", user.getLastName());
            userInfo.put("isActive", user.getIsActive());

            // 简化的时间处理
            if (user.getLastLogin() != null) {
                userInfo.put("lastLogin", user.getLastLogin().toString());
            } else {
                userInfo.put("lastLogin", null);
            }

            // 简化的角色处理
            userInfo.put("roles", List.of("USER", "ADMIN")); // 临时硬编码

            // 简化的部门处理
            userInfo.put("departmentName", "系统管理部");
            userInfo.put("departmentId", 1L);

            System.out.println("当前用户信息获取成功");
            return ResponseEntity.ok(ApiResponse.success("Current user info retrieved", userInfo));
        } catch (Exception e) {
            System.out.println("获取当前用户信息失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to get current user info", e.getMessage()));
        }
    }

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        try {
            System.out.println("=== 获取用户列表 ===");

            Sort sort = sortDir.equalsIgnoreCase("desc") ?
                       Sort.by(sortBy).descending() :
                       Sort.by(sortBy).ascending();

            Pageable pageable = PageRequest.of(page, size, sort);
            Page<User> users = userService.getAllUsers(pageable);

            System.out.println("找到用户数量: " + users.getTotalElements());

            // 创建简化的用户信息，避免序列化问题
            List<Map<String, Object>> simplifiedUsers = users.getContent().stream()
                    .map(user -> {
                        Map<String, Object> userInfo = new HashMap<>();
                        userInfo.put("id", user.getId());
                        userInfo.put("username", user.getUsername());
                        userInfo.put("email", user.getEmail());
                        userInfo.put("firstName", user.getFirstName());
                        userInfo.put("lastName", user.getLastName());
                        userInfo.put("isActive", user.getIsActive());
                        userInfo.put("isLocked", user.getIsLocked());
                        userInfo.put("lastLogin", user.getLastLogin() != null ? user.getLastLogin().toString() : null);
                        userInfo.put("createdAt", user.getCreatedAt() != null ? user.getCreatedAt().toString() : null);

                        // 简化角色信息
                        userInfo.put("roles", List.of("USER"));

                        // 简化部门信息
                        userInfo.put("departmentName", "未分配");
                        userInfo.put("departmentId", null);

                        return userInfo;
                    })
                    .toList();

            // 创建分页信息
            Map<String, Object> pageInfo = new HashMap<>();
            pageInfo.put("content", simplifiedUsers);
            pageInfo.put("totalElements", users.getTotalElements());
            pageInfo.put("totalPages", users.getTotalPages());
            pageInfo.put("size", users.getSize());
            pageInfo.put("number", users.getNumber());
            pageInfo.put("first", users.isFirst());
            pageInfo.put("last", users.isLast());

            System.out.println("用户列表获取成功");
            return ResponseEntity.ok(ApiResponse.success("Users retrieved successfully", pageInfo));
        } catch (Exception e) {
            System.out.println("获取用户列表失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to get users", e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA') or #id == authentication.principal.id")
    public ResponseEntity<ApiResponse<User>> getUserById(@PathVariable Long id) {
        Optional<User> user = userService.getUserById(id);
        
        if (user.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success("User found", user.get()));
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/username/{username}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<User>> getUserByUsername(@PathVariable String username) {
        Optional<User> user = userService.getUserByUsername(username);
        
        if (user.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success("User found", user.get()));
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/active")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getActiveUsers() {
        try {
            System.out.println("=== 获取活跃用户列表 ===");

            List<User> users = userService.getActiveUsers();
            System.out.println("找到活跃用户数量: " + users.size());

            // 创建简化的用户信息
            List<Map<String, Object>> simplifiedUsers = users.stream()
                    .map(user -> {
                        Map<String, Object> userInfo = new HashMap<>();
                        userInfo.put("id", user.getId());
                        userInfo.put("username", user.getUsername());
                        userInfo.put("email", user.getEmail());
                        userInfo.put("firstName", user.getFirstName());
                        userInfo.put("lastName", user.getLastName());
                        userInfo.put("isActive", user.getIsActive());
                        return userInfo;
                    })
                    .toList();

            System.out.println("活跃用户列表获取成功");
            return ResponseEntity.ok(ApiResponse.success("Active users retrieved", simplifiedUsers));
        } catch (Exception e) {
            System.out.println("获取活跃用户列表失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to get active users", e.getMessage()));
        }
    }

    @GetMapping("/department/{departmentId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<List<User>>> getUsersByDepartment(@PathVariable Long departmentId) {
        List<User> users = userService.getUsersByDepartment(departmentId);
        return ResponseEntity.ok(ApiResponse.success("Department users retrieved", users));
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> createUser(@RequestBody Map<String, Object> userData) {
        try {
            System.out.println("=== 创建用户开始 ===");
            System.out.println("用户数据: " + userData);

            // 验证必需字段
            String username = (String) userData.get("username");
            String password = (String) userData.get("password");
            String email = (String) userData.get("email");
            String firstName = (String) userData.get("firstName");
            String lastName = (String) userData.get("lastName");

            // 使用统一的参数验证
            validateUserInput(username, password, email);

            // 创建SignupRequest对象
            SignupRequest signupRequest = new SignupRequest();
            signupRequest.setUsername(username.trim());
            signupRequest.setPassword(password);
            signupRequest.setEmail(email.trim());
            signupRequest.setFirstName(firstName != null ? firstName.trim() : "");
            signupRequest.setLastName(lastName != null ? lastName.trim() : "");

            System.out.println("SignupRequest创建完成: " + signupRequest.getUsername());

            User user = userService.createUser(signupRequest);

            // 创建简化的响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", user.getId());
            responseData.put("username", user.getUsername());
            responseData.put("email", user.getEmail());
            responseData.put("firstName", user.getFirstName());
            responseData.put("lastName", user.getLastName());
            responseData.put("isActive", user.getIsActive());
            responseData.put("createdAt", user.getCreatedAt().toString());

            System.out.println("用户创建成功: " + user.getId());
            return ResponseEntity.ok(ApiResponse.success("User created successfully", responseData));
        } catch (Exception e) {
            System.out.println("创建用户失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to create user", e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or #id == authentication.principal.id")
    public ResponseEntity<ApiResponse<User>> updateUser(@PathVariable Long id,
                                                       @Valid @RequestBody UserUpdateRequest userUpdateRequest) {
        try {
            User updatedUser = userService.updateUserProfile(id, userUpdateRequest);
            return ResponseEntity.ok(ApiResponse.success("User updated successfully", updatedUser));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update user", e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return ResponseEntity.ok(ApiResponse.success("User deleted successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete user", e.getMessage()));
        }
    }

    @PostMapping("/{id}/activate")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> activateUser(@PathVariable Long id) {
        try {
            userService.activateUser(id);
            return ResponseEntity.ok(ApiResponse.success("User activated successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to activate user", e.getMessage()));
        }
    }

    @PostMapping("/{id}/lock")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> lockUser(@PathVariable Long id) {
        try {
            userService.lockUser(id);
            return ResponseEntity.ok(ApiResponse.success("User locked successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to lock user", e.getMessage()));
        }
    }

    @PostMapping("/{id}/unlock")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> unlockUser(@PathVariable Long id) {
        try {
            userService.unlockUser(id);
            return ResponseEntity.ok(ApiResponse.success("User unlocked successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to unlock user", e.getMessage()));
        }
    }

    @PostMapping("/{id}/change-password")
    @PreAuthorize("hasRole('ADMIN') or #id == authentication.principal.id")
    public ResponseEntity<ApiResponse<Void>> changePassword(@PathVariable Long id,
                                                           @Valid @RequestBody PasswordChangeRequest passwordChangeRequest) {
        try {
            userService.changePassword(id, passwordChangeRequest);
            return ResponseEntity.ok(ApiResponse.success("Password changed successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to change password", e.getMessage()));
        }
    }

    @PostMapping("/{id}/reset-password")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> resetPassword(@PathVariable Long id,
                                                          @RequestBody Map<String, String> request) {
        try {
            String newPassword = request.get("newPassword");
            if (newPassword == null || newPassword.length() < 6) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Password must be at least 6 characters long"));
            }

            userService.changePassword(id, newPassword);
            return ResponseEntity.ok(ApiResponse.success("Password reset successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to reset password", e.getMessage()));
        }
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Page<User>>> searchUsers(
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) String firstName,
            @RequestParam(required = false) String lastName,
            @RequestParam(required = false) Boolean isActive,
            @RequestParam(required = false) Long departmentId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : 
                   Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<User> users = userService.searchUsers(username, email, firstName, 
                                                  lastName, isActive, departmentId, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("Search completed", users));
    }

    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Map<String, Long>>> getUserStats() {
        Map<String, Long> stats = Map.of(
            "activeUsers", userService.getActiveUserCount(),
            "lockedUsers", userService.getLockedUserCount(),
            "totalUsers", (long) userService.getAllUsers().size()
        );
        
        return ResponseEntity.ok(ApiResponse.success("User statistics", stats));
    }

    /**
     * 验证用户输入参数
     */
    private void validateUserInput(String username, String password, String email) {
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }

        if (password == null || password.length() < 8) {
            throw new IllegalArgumentException("密码至少需要8个字符");
        }

        // 增强密码复杂度验证
        if (!isValidPassword(password)) {
            throw new IllegalArgumentException("密码必须包含大写字母、小写字母、数字和特殊字符");
        }

        if (email == null || !isValidEmail(email)) {
            throw new IllegalArgumentException("请输入有效的邮箱地址");
        }
    }

    /**
     * 验证密码复杂度
     */
    private boolean isValidPassword(String password) {
        if (password.length() < 8) return false;

        boolean hasUpper = password.chars().anyMatch(Character::isUpperCase);
        boolean hasLower = password.chars().anyMatch(Character::isLowerCase);
        boolean hasDigit = password.chars().anyMatch(Character::isDigit);
        boolean hasSpecial = password.chars().anyMatch(ch -> "!@#$%^&*()_+-=[]{}|;:,.<>?".indexOf(ch) >= 0);

        return hasUpper && hasLower && hasDigit && hasSpecial;
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        return email != null &&
               email.contains("@") &&
               email.contains(".") &&
               email.length() > 5 &&
               email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }
}
