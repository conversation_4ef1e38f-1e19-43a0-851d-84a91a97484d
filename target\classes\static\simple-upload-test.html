<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单上传测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; white-space: pre-wrap; font-family: monospace; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
        .warning { background: #fff3e0; color: #f57c00; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 3px; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        input[type="file"], input[type="text"] { margin: 10px 0; padding: 8px; width: 300px; border: 1px solid #ddd; border-radius: 3px; }
        .upload-area { border: 2px dashed #ccc; padding: 20px; text-align: center; margin: 10px 0; }
        .upload-area.dragover { border-color: #007bff; background: #f0f8ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 DMS简单上传测试</h1>
        
        <div class="section">
            <h2>1. 快速登录</h2>
            <button onclick="login()" id="loginBtn">登录 (admin/admin123)</button>
            <div id="loginResult" class="result"></div>
        </div>

        <div class="section">
            <h2>2. 文件上传测试</h2>
            <div class="upload-area" id="uploadArea">
                <p>拖拽文件到这里或点击选择文件</p>
                <input type="file" id="fileInput" accept=".txt,.pdf,.doc,.docx,.jpg,.png" style="display: none;">
                <button onclick="document.getElementById('fileInput').click()">选择文件</button>
            </div>
            
            <input type="text" id="titleInput" placeholder="文档标题" value="测试文档">
            <input type="text" id="descInput" placeholder="文档描述" value="简单上传测试">
            <br>
            <button onclick="uploadFile()" id="uploadBtn">上传文件</button>
            <button onclick="checkUploadStatus()" id="statusBtn">检查状态</button>
            
            <div id="uploadResult" class="result"></div>
        </div>

        <div class="section">
            <h2>3. 上传历史</h2>
            <button onclick="getDocumentList()">获取文档列表</button>
            <div id="listResult" class="result"></div>
        </div>
    </div>

    <script>
        let token = null;
        let selectedFile = null;

        function log(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = 'result ' + type;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function addLog(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.innerHTML += `[${timestamp}] ${message}\n`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function login() {
            const btn = document.getElementById('loginBtn');
            btn.disabled = true;
            btn.textContent = '登录中...';

            try {
                const response = await fetch('/dms/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });

                if (response.ok) {
                    const result = await response.json();
                    token = result.data.token;
                    localStorage.setItem('token', token);
                    log('loginResult', `✅ 登录成功\n用户: ${result.data.username}\nToken长度: ${token.length}`, 'success');
                } else {
                    const error = await response.text();
                    log('loginResult', `❌ 登录失败: ${error}`, 'error');
                }
            } catch (error) {
                log('loginResult', `❌ 登录异常: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '登录 (admin/admin123)';
            }
        }

        async function uploadFile() {
            if (!token) {
                log('uploadResult', '❌ 请先登录', 'error');
                return;
            }

            if (!selectedFile) {
                log('uploadResult', '❌ 请选择文件', 'error');
                return;
            }

            const btn = document.getElementById('uploadBtn');
            btn.disabled = true;
            btn.textContent = '上传中...';

            try {
                log('uploadResult', '开始上传...', 'warning');
                
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('title', document.getElementById('titleInput').value);
                formData.append('description', document.getElementById('descInput').value);

                console.log('=== 上传详情 ===');
                console.log('文件:', selectedFile.name, selectedFile.size, selectedFile.type);
                console.log('Token:', token.substring(0, 30) + '...');

                const startTime = Date.now();
                const response = await fetch('/dms/api/documents/upload', {
                    method: 'POST',
                    headers: { 'Authorization': 'Bearer ' + token },
                    body: formData
                });
                const endTime = Date.now();

                console.log('响应状态:', response.status);
                console.log('响应时间:', endTime - startTime, 'ms');
                console.log('响应头:', [...response.headers.entries()]);

                const responseText = await response.text();
                console.log('响应内容:', responseText);

                if (response.ok) {
                    try {
                        const result = JSON.parse(responseText);
                        log('uploadResult', 
                            `✅ 上传成功！\n` +
                            `文档ID: ${result.data.id}\n` +
                            `标题: ${result.data.title}\n` +
                            `文件名: ${result.data.originalFileName}\n` +
                            `大小: ${result.data.fileSize} 字节\n` +
                            `状态: ${result.data.status}\n` +
                            `耗时: ${endTime - startTime}ms`, 
                            'success'
                        );
                    } catch (parseError) {
                        log('uploadResult', `✅ 上传成功但响应解析失败\n响应: ${responseText}`, 'warning');
                    }
                } else {
                    log('uploadResult', `❌ 上传失败 (${response.status})\n响应: ${responseText}`, 'error');
                }
            } catch (error) {
                console.error('上传异常:', error);
                log('uploadResult', `❌ 上传异常: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '上传文件';
            }
        }

        async function checkUploadStatus() {
            if (!token) {
                log('uploadResult', '❌ 请先登录', 'error');
                return;
            }

            try {
                addLog('uploadResult', '检查上传状态...', 'warning');
                
                // 检查uploads目录中的文件数量（通过API）
                const response = await fetch('/dms/api/documents?page=0&size=1', {
                    method: 'GET',
                    headers: { 'Authorization': 'Bearer ' + token }
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('uploadResult', `📊 数据库中文档总数: ${result.data.totalElements}`, 'success');
                } else {
                    addLog('uploadResult', `❌ 无法获取文档统计`, 'error');
                }
            } catch (error) {
                addLog('uploadResult', `❌ 状态检查异常: ${error.message}`, 'error');
            }
        }

        async function getDocumentList() {
            if (!token) {
                log('listResult', '❌ 请先登录', 'error');
                return;
            }

            try {
                log('listResult', '获取文档列表...', 'warning');
                
                const response = await fetch('/dms/api/documents?page=0&size=10', {
                    method: 'GET',
                    headers: { 'Authorization': 'Bearer ' + token }
                });

                if (response.ok) {
                    const result = await response.json();
                    const docs = result.data.content;
                    
                    let message = `📋 文档列表 (总数: ${result.data.totalElements})\n\n`;
                    docs.forEach((doc, index) => {
                        message += `${index + 1}. ${doc.title}\n`;
                        message += `   ID: ${doc.id} | 文件: ${doc.originalFileName}\n`;
                        message += `   大小: ${doc.fileSize} 字节 | 状态: ${doc.status}\n`;
                        message += `   创建: ${doc.createdAt}\n\n`;
                    });
                    
                    log('listResult', message, 'success');
                } else {
                    const error = await response.text();
                    log('listResult', `❌ 获取列表失败: ${error}`, 'error');
                }
            } catch (error) {
                log('listResult', `❌ 获取列表异常: ${error.message}`, 'error');
            }
        }

        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFile = e.target.files[0];
            if (selectedFile) {
                document.getElementById('uploadArea').innerHTML = 
                    `<p>✅ 已选择文件: ${selectedFile.name}</p>` +
                    `<p>大小: ${(selectedFile.size / 1024).toFixed(2)} KB</p>` +
                    `<button onclick="document.getElementById('fileInput').click()">重新选择</button>`;
                
                // 自动设置标题
                const titleInput = document.getElementById('titleInput');
                if (titleInput.value === '测试文档') {
                    titleInput.value = selectedFile.name.replace(/\.[^/.]+$/, "");
                }
            }
        });

        // 拖拽上传
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                selectedFile = files[0];
                document.getElementById('fileInput').files = files;
                document.getElementById('fileInput').dispatchEvent(new Event('change'));
            }
        });

        // 页面加载时检查token
        window.onload = function() {
            const savedToken = localStorage.getItem('token');
            if (savedToken) {
                token = savedToken;
                log('loginResult', '✅ 已从本地存储加载Token', 'success');
            }
        };
    </script>
</body>
</html>
