package com.pharma.dms.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pharma.dms.entity.User;
import com.pharma.dms.repository.UserRepository;

@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    @Autowired
    UserRepository userRepository;

    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        long startTime = System.currentTimeMillis();

        // 使用优化的查询，预加载角色信息
        User user = userRepository.findByUsernameWithRoles(username)
                .orElseThrow(() -> new UsernameNotFoundException("User Not Found with username: " + username));

        UserPrincipal userPrincipal = UserPrincipal.create(user);

        long duration = System.currentTimeMillis() - startTime;
        System.out.println("🔍 用户详情加载耗时: " + duration + "ms，用户: " + username);

        return userPrincipal;
    }
}
