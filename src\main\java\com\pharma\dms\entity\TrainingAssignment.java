package com.pharma.dms.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

@Entity
@Table(name = "training_assignments", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"course_id", "user_id"}))
public class TrainingAssignment extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "course_id", nullable = false)
    private TrainingCourse course;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assigned_by")
    private User assignedBy;

    @Column(name = "assigned_date", nullable = false)
    private LocalDateTime assignedDate;

    @Column(name = "due_date")
    private LocalDateTime dueDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AssignmentStatus status = AssignmentStatus.ASSIGNED;

    @Enumerated(EnumType.STRING)
    @Column(name = "priority", nullable = false)
    private Priority priority = Priority.NORMAL;

    @Column(name = "is_mandatory", nullable = false)
    private Boolean isMandatory = false;

    @Column(name = "auto_assigned", nullable = false)
    private Boolean autoAssigned = false;

    @Column(name = "reminder_sent", nullable = false)
    private Boolean reminderSent = false;

    @Column(name = "last_reminder_date")
    private LocalDateTime lastReminderDate;

    @Column(name = "completion_date")
    private LocalDateTime completionDate;

    @Column(name = "notes")
    private String notes;

    // 新增字段：批量分配相关
    @Column(name = "batch_id")
    private String batchId; // 批量分配的批次ID

    @Column(name = "assignment_reason")
    private String assignmentReason; // 分配原因

    @Enumerated(EnumType.STRING)
    @Column(name = "assignment_type")
    private AssignmentType assignmentType = AssignmentType.MANUAL; // 分配类型

    @Column(name = "estimated_completion_hours")
    private Double estimatedCompletionHours; // 预计完成时长

    @Column(name = "actual_completion_hours")
    private Double actualCompletionHours; // 实际完成时长

    @Column(name = "notification_sent", nullable = false)
    private Boolean notificationSent = false; // 是否已发送通知

    @Column(name = "last_accessed_date")
    private LocalDateTime lastAccessedDate; // 最后访问日期

    @Column(name = "progress_percentage")
    private Integer progressPercentage = 0; // 进度百分比

    @Column(name = "exemption_reason")
    private String exemptionReason; // 豁免原因（如果状态为EXEMPTED）

    // Constructors
    public TrainingAssignment() {
        this.assignedDate = LocalDateTime.now();
    }

    public TrainingAssignment(TrainingCourse course, User user) {
        this();
        this.course = course;
        this.user = user;
    }

    public TrainingAssignment(TrainingCourse course, User user, User assignedBy) {
        this(course, user);
        this.assignedBy = assignedBy;
    }

    // Getters and Setters
    public TrainingCourse getCourse() {
        return course;
    }

    public void setCourse(TrainingCourse course) {
        this.course = course;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public User getAssignedBy() {
        return assignedBy;
    }

    public void setAssignedBy(User assignedBy) {
        this.assignedBy = assignedBy;
    }

    public LocalDateTime getAssignedDate() {
        return assignedDate;
    }

    public void setAssignedDate(LocalDateTime assignedDate) {
        this.assignedDate = assignedDate;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public AssignmentStatus getStatus() {
        return status;
    }

    public void setStatus(AssignmentStatus status) {
        this.status = status;
    }

    public Priority getPriority() {
        return priority;
    }

    public void setPriority(Priority priority) {
        this.priority = priority;
    }

    public Boolean getIsMandatory() {
        return isMandatory;
    }

    public void setIsMandatory(Boolean isMandatory) {
        this.isMandatory = isMandatory;
    }

    public Boolean getAutoAssigned() {
        return autoAssigned;
    }

    public void setAutoAssigned(Boolean autoAssigned) {
        this.autoAssigned = autoAssigned;
    }

    public Boolean getReminderSent() {
        return reminderSent;
    }

    public void setReminderSent(Boolean reminderSent) {
        this.reminderSent = reminderSent;
    }

    public LocalDateTime getLastReminderDate() {
        return lastReminderDate;
    }

    public void setLastReminderDate(LocalDateTime lastReminderDate) {
        this.lastReminderDate = lastReminderDate;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    // 新增字段的getter和setter
    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getAssignmentReason() {
        return assignmentReason;
    }

    public void setAssignmentReason(String assignmentReason) {
        this.assignmentReason = assignmentReason;
    }

    public AssignmentType getAssignmentType() {
        return assignmentType;
    }

    public void setAssignmentType(AssignmentType assignmentType) {
        this.assignmentType = assignmentType;
    }

    public Double getEstimatedCompletionHours() {
        return estimatedCompletionHours;
    }

    public void setEstimatedCompletionHours(Double estimatedCompletionHours) {
        this.estimatedCompletionHours = estimatedCompletionHours;
    }

    public Double getActualCompletionHours() {
        return actualCompletionHours;
    }

    public void setActualCompletionHours(Double actualCompletionHours) {
        this.actualCompletionHours = actualCompletionHours;
    }

    public Boolean getNotificationSent() {
        return notificationSent;
    }

    public void setNotificationSent(Boolean notificationSent) {
        this.notificationSent = notificationSent;
    }

    public LocalDateTime getLastAccessedDate() {
        return lastAccessedDate;
    }

    public void setLastAccessedDate(LocalDateTime lastAccessedDate) {
        this.lastAccessedDate = lastAccessedDate;
    }

    public Integer getProgressPercentage() {
        return progressPercentage;
    }

    public void setProgressPercentage(Integer progressPercentage) {
        this.progressPercentage = progressPercentage;
    }

    public String getExemptionReason() {
        return exemptionReason;
    }

    public void setExemptionReason(String exemptionReason) {
        this.exemptionReason = exemptionReason;
    }

    // Helper methods
    public boolean isOverdue() {
        return dueDate != null && LocalDateTime.now().isAfter(dueDate) && 
               status != AssignmentStatus.COMPLETED;
    }

    public boolean isCompleted() {
        return status == AssignmentStatus.COMPLETED;
    }

    public boolean isDueSoon() {
        if (dueDate == null || isCompleted()) return false;
        return LocalDateTime.now().plusDays(3).isAfter(dueDate);
    }

    public long getDaysUntilDue() {
        if (dueDate == null) return -1;
        return java.time.temporal.ChronoUnit.DAYS.between(LocalDateTime.now(), dueDate);
    }

    public void markCompleted() {
        this.status = AssignmentStatus.COMPLETED;
        this.completionDate = LocalDateTime.now();
    }

    // Enums
    public enum AssignmentStatus {
        ASSIGNED("Assigned"),
        IN_PROGRESS("In Progress"),
        COMPLETED("Completed"),
        OVERDUE("Overdue"),
        CANCELLED("Cancelled"),
        EXEMPTED("Exempted");

        private final String displayName;

        AssignmentStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    public enum Priority {
        LOW("Low"),
        NORMAL("Normal"),
        HIGH("High"),
        URGENT("Urgent");

        private final String displayName;

        Priority(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    public enum AssignmentType {
        MANUAL("手动分配"),
        AUTO("自动分配"),
        BATCH("批量分配"),
        ROLE_BASED("基于角色分配"),
        DEPARTMENT_BASED("基于部门分配"),
        MANDATORY("强制分配"),
        OPTIONAL("可选分配");

        private final String displayName;

        AssignmentType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
