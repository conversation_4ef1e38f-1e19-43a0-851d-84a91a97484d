/**
 * UI增强功能 - 提升用户体验
 */

// 全局UI增强对象
const UIEnhancements = {

    // 存储事件监听器引用，用于清理
    eventListeners: new Map(),

    // 初始化所有增强功能
    init() {
        this.initSmoothScrolling();
        this.initTooltips();
        this.initLoadingStates();
        this.initFormValidation();
        this.initTableEnhancements();
        this.initCardAnimations();
        this.initNotifications();
        this.setupCleanup();
        console.log('✅ UI增强功能已初始化');
    },

    // 设置清理机制
    setupCleanup() {
        // 页面卸载时清理所有事件监听器
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    },

    // 清理所有事件监听器
    cleanup() {
        this.eventListeners.forEach((listener, element) => {
            if (element && listener) {
                element.removeEventListener(listener.event, listener.handler);
            }
        });
        this.eventListeners.clear();
        console.log('🧹 UI增强功能已清理');
    },

    // 添加事件监听器并记录引用
    addEventListenerWithCleanup(element, event, handler) {
        element.addEventListener(event, handler);
        this.eventListeners.set(element, { event, handler });
    },

    // 平滑滚动
    initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            const handler = function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            };
            this.addEventListenerWithCleanup(anchor, 'click', handler);
        });
    },

    // 初始化工具提示
    initTooltips() {
        // 为所有带有title属性的元素添加Bootstrap tooltip
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"], [title]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },

    // 加载状态管理
    initLoadingStates() {
        // 为所有按钮添加加载状态功能
        const handler = function(e) {
            const button = e.target.closest('button[type="submit"], .btn-loading');
            if (button && !button.disabled) {
                UIEnhancements.showButtonLoading(button);
            }
        };
        this.addEventListenerWithCleanup(document, 'click', handler);
    },

    // 显示按钮加载状态
    showButtonLoading(button, text = '处理中...') {
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = `<span class="spinner-border spinner-border-sm me-2" role="status"></span>${text}`;
        
        // 存储原始文本以便恢复
        button.dataset.originalText = originalText;
        
        // 10秒后自动恢复（防止卡死）
        setTimeout(() => {
            this.hideButtonLoading(button);
        }, 10000);
    },

    // 隐藏按钮加载状态
    hideButtonLoading(button) {
        if (button.dataset.originalText) {
            button.innerHTML = button.dataset.originalText;
            button.disabled = false;
            delete button.dataset.originalText;
        }
    },

    // 表单验证增强
    initFormValidation() {
        const forms = document.querySelectorAll('.needs-validation');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 滚动到第一个错误字段
                    const firstInvalid = form.querySelector(':invalid');
                    if (firstInvalid) {
                        firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstInvalid.focus();
                    }
                }
                form.classList.add('was-validated');
            });

            // 实时验证
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.checkValidity()) {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    } else {
                        this.classList.remove('is-valid');
                        this.classList.add('is-invalid');
                    }
                });
            });
        });
    },

    // 表格增强
    initTableEnhancements() {
        const tables = document.querySelectorAll('.table-enhanced');
        tables.forEach(table => {
            // 添加行悬停效果
            table.classList.add('table-hover');
            
            // 添加排序功能
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    this.sortTable(table, header);
                });
            });
        });
    },

    // 表格排序
    sortTable(table, header) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = header.classList.contains('sort-asc');
        
        rows.sort((a, b) => {
            const aText = a.children[columnIndex].textContent.trim();
            const bText = b.children[columnIndex].textContent.trim();
            
            if (isAscending) {
                return bText.localeCompare(aText);
            } else {
                return aText.localeCompare(bText);
            }
        });
        
        // 更新排序图标
        table.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
        
        // 重新排列行
        rows.forEach(row => tbody.appendChild(row));
    },

    // 卡片动画
    initCardAnimations() {
        const cards = document.querySelectorAll('.card');
        
        // 使用Intersection Observer实现滚动动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });

        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    },

    // 通知系统
    initNotifications() {
        // 创建通知容器
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 350px;
            `;
            document.body.appendChild(container);
        }
    },

    // 显示通知
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notification-container');
        const notification = document.createElement('div');
        
        const typeClasses = {
            success: 'alert-success',
            error: 'alert-danger',
            warning: 'alert-warning',
            info: 'alert-info'
        };
        
        notification.className = `alert ${typeClasses[type]} alert-dismissible fade show`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        container.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    },

    // 确认对话框
    confirm(message, callback) {
        if (window.confirm(message)) {
            callback();
        }
    },

    // 格式化数字
    formatNumber(num) {
        return new Intl.NumberFormat('zh-CN').format(num);
    },

    // 格式化日期
    formatDate(date) {
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    UIEnhancements.init();
});

// 导出到全局
window.UIEnhancements = UIEnhancements;
