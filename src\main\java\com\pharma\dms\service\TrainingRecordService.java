package com.pharma.dms.service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pharma.dms.entity.AuditLog;
import com.pharma.dms.entity.TrainingAssignment;
import com.pharma.dms.entity.TrainingCourse;
import com.pharma.dms.entity.TrainingRecord;
import com.pharma.dms.entity.User;
import com.pharma.dms.exception.BusinessException;
import com.pharma.dms.repository.TrainingAssignmentRepository;
import com.pharma.dms.repository.TrainingCourseRepository;
import com.pharma.dms.repository.TrainingRecordRepository;
import com.pharma.dms.repository.UserRepository;

@Service
@Transactional
public class TrainingRecordService {

    @Autowired
    private TrainingRecordRepository recordRepository;

    @Autowired
    private TrainingCourseRepository courseRepository;

    @Autowired
    private TrainingAssignmentRepository assignmentRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AuditService auditService;

    @Value("${app.training.certificate-dir:certificates}")
    private String certificateDir;

    // Training record CRUD operations
    public List<TrainingRecord> getAllRecords() {
        return recordRepository.findAll();
    }

    public Page<TrainingRecord> getAllRecords(Pageable pageable) {
        return recordRepository.findAll(pageable);
    }

    public Optional<TrainingRecord> getRecordById(Long id) {
        return recordRepository.findById(id);
    }

    public List<TrainingRecord> getRecordsByUser(User user) {
        return recordRepository.findByUser(user);
    }

    public List<TrainingRecord> getRecordsByCourse(TrainingCourse course) {
        return recordRepository.findByCourse(course);
    }

    public Optional<TrainingRecord> getRecordByCourseAndUser(TrainingCourse course, User user) {
        return recordRepository.findByCourseAndUser(course, user);
    }

    // Training initiation
    public TrainingRecord startTraining(Long courseId, Long userId) {
        TrainingCourse course = courseRepository.findById(courseId)
                .orElseThrow(() -> new RuntimeException("Course not found"));
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Check if course is active and effective
        if (!course.isActive() || !course.isEffective()) {
            throw new RuntimeException("Course is not available for training");
        }

        // Check if user already has a training record
        Optional<TrainingRecord> existingRecord = recordRepository.findByCourseAndUser(course, user);
        if (existingRecord.isPresent()) {
            TrainingRecord record = existingRecord.get();
            if (record.getStatus() == TrainingRecord.TrainingStatus.COMPLETED ||
                record.getStatus() == TrainingRecord.TrainingStatus.CERTIFIED) {
                throw new RuntimeException("Training already completed");
            }
            // Resume existing training
            record.setStatus(TrainingRecord.TrainingStatus.IN_PROGRESS);
            return recordRepository.save(record);
        }

        // Create new training record
        TrainingRecord record = new TrainingRecord(course, user);
        record.setStatus(TrainingRecord.TrainingStatus.IN_PROGRESS);
        record.setInstructor(course.getInstructor());

        // Link to assignment if exists - 使用悲观锁防止并发问题
        Optional<TrainingAssignment> assignment = assignmentRepository.findByCourseAndUser(course, user);
        assignment.ifPresent(ta -> {
            // 检查当前状态，避免重复更新
            if (ta.getStatus() != TrainingAssignment.AssignmentStatus.IN_PROGRESS) {
                record.setAssignment(ta);
                ta.setStatus(TrainingAssignment.AssignmentStatus.IN_PROGRESS);
                // 更新分配时间（如果字段存在）
                ta.setAssignedDate(LocalDateTime.now());

                try {
                    assignmentRepository.save(ta);
                } catch (Exception e) {
                    // 处理并发更新异常
                    throw new BusinessException("培训任务状态更新失败，可能存在并发操作", e);
                }
            }
        });

        TrainingRecord savedRecord = recordRepository.save(record);

        // Log audit event
        auditService.logSystemEvent("TRAINING_STARTED", 
                "Training started: " + course.getTitle() + " by " + user.getUsername(), 
                AuditLog.Severity.INFO);

        return savedRecord;
    }

    // Training completion and scoring
    public TrainingRecord completeTraining(Long recordId, Integer finalScore, String feedback) {
        TrainingRecord record = recordRepository.findById(recordId)
                .orElseThrow(() -> new RuntimeException("Training record not found"));

        if (record.getStatus() == TrainingRecord.TrainingStatus.COMPLETED) {
            throw new RuntimeException("Training already completed");
        }

        record.setFinalScore(finalScore);
        record.setFeedback(feedback);
        record.setCompletionDate(LocalDateTime.now());
        record.incrementAttempts();

        // Determine if passed
        boolean passed = finalScore >= record.getPassingScore();
        
        if (passed) {
            record.setStatus(TrainingRecord.TrainingStatus.COMPLETED);
            
            // Update assignment status
            if (record.getAssignment() != null) {
                record.getAssignment().markCompleted();
                assignmentRepository.save(record.getAssignment());
            }
            
            // Generate certificate if course requires it
            if (record.getCourse().getValidityPeriodMonths() != null) {
                generateCertificate(record);
            }
        } else {
            // Check if can retake
            if (record.canRetake()) {
                record.setStatus(TrainingRecord.TrainingStatus.IN_PROGRESS);
            } else {
                record.setStatus(TrainingRecord.TrainingStatus.FAILED);
                
                // Mark as retrain required if mandatory
                if (record.getCourse().getIsMandatory()) {
                    record.setIsRetrainRequired(true);
                    record.setRetrainReason("Failed to achieve passing score after maximum attempts");
                    record.setRetrainDueDate(LocalDateTime.now().plusDays(30));
                }
            }
        }

        TrainingRecord savedRecord = recordRepository.save(record);

        // Log audit event
        String eventType = passed ? "TRAINING_COMPLETED" : "TRAINING_FAILED";
        auditService.logSystemEvent(eventType, 
                "Training " + (passed ? "completed" : "failed") + ": " + 
                record.getCourse().getTitle() + " by " + record.getUser().getUsername() + 
                " (Score: " + finalScore + ")", 
                passed ? AuditLog.Severity.INFO : AuditLog.Severity.WARNING);

        return savedRecord;
    }

    // Electronic signature for GMP compliance
    public TrainingRecord addElectronicSignature(Long recordId, String signature, 
                                                String ipAddress, String userAgent) {
        TrainingRecord record = recordRepository.findById(recordId)
                .orElseThrow(() -> new RuntimeException("Training record not found"));

        if (record.getStatus() != TrainingRecord.TrainingStatus.COMPLETED) {
            throw new RuntimeException("Training must be completed before signing");
        }

        if (record.hasElectronicSignature()) {
            throw new RuntimeException("Training record already has electronic signature");
        }

        // Generate signature hash for integrity
        String signatureData = record.getUser().getUsername() + "|" + 
                              record.getCourse().getCourseCode() + "|" + 
                              record.getCompletionDate().toString() + "|" + 
                              signature;
        String signatureHash = calculateHash(signatureData);

        record.setElectronicSignature(signature);
        record.setSignatureDate(LocalDateTime.now());
        record.setSignatureHash(signatureHash);
        record.setSignatureIpAddress(ipAddress);
        record.setSignatureUserAgent(userAgent);
        record.setStatus(TrainingRecord.TrainingStatus.CERTIFIED);

        TrainingRecord savedRecord = recordRepository.save(record);

        // Log audit event
        auditService.logSystemEvent("TRAINING_SIGNED", 
                "Electronic signature added to training record: " + 
                record.getCourse().getTitle() + " by " + record.getUser().getUsername(), 
                AuditLog.Severity.INFO);

        return savedRecord;
    }

    // Certificate generation
    private void generateCertificate(TrainingRecord record) {
        String certificateNumber = generateCertificateNumber(record);
        record.setCertificateNumber(certificateNumber);
        record.setCertificateIssueDate(LocalDateTime.now());
        
        // Set expiry date based on course validity period
        if (record.getCourse().getValidityPeriodMonths() != null) {
            record.setCertificateExpiryDate(
                LocalDateTime.now().plusMonths(record.getCourse().getValidityPeriodMonths()));
        }

        // Generate certificate hash for integrity
        String certificateData = certificateNumber + "|" + 
                               record.getUser().getUsername() + "|" + 
                               record.getCourse().getCourseCode() + "|" + 
                               record.getCertificateIssueDate().toString();
        record.setCertificateHash(calculateHash(certificateData));

        // TODO: Generate actual certificate PDF file
        // record.setCertificatePath(generateCertificatePDF(record));
    }

    private String generateCertificateNumber(TrainingRecord record) {
        String prefix = "CERT";
        String courseCode = record.getCourse().getCourseCode();
        String userCode = record.getUser().getUsername().toUpperCase();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String uniqueId = UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        
        return String.format("%s-%s-%s-%s-%s", prefix, courseCode, userCode, timestamp, uniqueId);
    }

    // Certificate validation
    public boolean validateCertificate(String certificateNumber) {
        Optional<TrainingRecord> record = recordRepository.findByCertificateNumber(certificateNumber);
        if (record.isEmpty()) {
            return false;
        }

        TrainingRecord tr = record.get();
        
        // Check if certificate is expired
        if (tr.isCertificateExpired()) {
            return false;
        }

        // Verify certificate hash
        String certificateData = certificateNumber + "|" + 
                               tr.getUser().getUsername() + "|" + 
                               tr.getCourse().getCourseCode() + "|" + 
                               tr.getCertificateIssueDate().toString();
        String expectedHash = calculateHash(certificateData);
        
        return expectedHash.equals(tr.getCertificateHash());
    }

    // Retrain management
    public void scheduleRetrain(Long recordId, String reason, LocalDateTime dueDate) {
        TrainingRecord record = recordRepository.findById(recordId)
                .orElseThrow(() -> new RuntimeException("Training record not found"));

        record.setIsRetrainRequired(true);
        record.setRetrainReason(reason);
        record.setRetrainDueDate(dueDate);
        record.setStatus(TrainingRecord.TrainingStatus.RETRAIN_REQUIRED);

        recordRepository.save(record);

        // Create new assignment for retrain
        TrainingAssignment retrainAssignment = new TrainingAssignment(record.getCourse(), record.getUser());
        retrainAssignment.setDueDate(dueDate);
        retrainAssignment.setIsMandatory(true);
        retrainAssignment.setPriority(TrainingAssignment.Priority.HIGH);
        retrainAssignment.setNotes("Retrain required: " + reason);
        assignmentRepository.save(retrainAssignment);

        // Log audit event
        auditService.logSystemEvent("RETRAIN_SCHEDULED", 
                "Retrain scheduled: " + record.getCourse().getTitle() + " for " + 
                record.getUser().getUsername() + " - Reason: " + reason, 
                AuditLog.Severity.WARNING);
    }

    // Search and filtering
    public Page<TrainingRecord> searchRecords(Long userId, Long courseId, 
                                            TrainingRecord.TrainingStatus status,
                                            LocalDateTime startDate, LocalDateTime endDate,
                                            Long instructorId, Pageable pageable) {
        return recordRepository.findTrainingRecordsWithFilters(userId, courseId, status, 
                                                             startDate, endDate, instructorId, pageable);
    }

    // Statistics and reporting
    public long getTotalRecordCount() {
        return recordRepository.count();
    }

    public long getRecordCountByStatus(TrainingRecord.TrainingStatus status) {
        return recordRepository.countByStatus(status);
    }

    public List<TrainingRecord> getExpiringCertificates(int days) {
        LocalDateTime startDate = LocalDateTime.now();
        LocalDateTime endDate = startDate.plusDays(days);
        return recordRepository.findCertificatesExpiringBetween(startDate, endDate);
    }

    public List<TrainingRecord> getRetrainRequired() {
        return recordRepository.findRetrainRequired();
    }

    public List<TrainingRecord> getOverdueRetraining() {
        return recordRepository.findOverdueRetraining(LocalDateTime.now());
    }

    public Double getAverageScoreByCourse(Long courseId) {
        return recordRepository.getAverageScoreByCourse(courseId);
    }

    public Double getAverageScoreByUser(Long userId) {
        return recordRepository.getAverageScoreByUser(userId);
    }

    // Utility methods
    private String calculateHash(String data) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(data.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Error calculating hash", e);
        }
    }
}
