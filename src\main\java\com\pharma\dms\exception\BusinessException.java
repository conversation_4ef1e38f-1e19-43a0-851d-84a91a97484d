package com.pharma.dms.exception;

import org.springframework.http.HttpStatus;

/**
 * 业务异常
 * 用于处理业务逻辑相关的异常情况
 */
public class BusinessException extends RuntimeException {
    
    private final HttpStatus status;
    private final Object data;

    public BusinessException(String message) {
        super(message);
        this.status = HttpStatus.BAD_REQUEST;
        this.data = null;
    }

    public BusinessException(String message, HttpStatus status) {
        super(message);
        this.status = status;
        this.data = null;
    }

    public BusinessException(String message, Object data) {
        super(message);
        this.status = HttpStatus.BAD_REQUEST;
        this.data = data;
    }

    public BusinessException(String message, HttpStatus status, Object data) {
        super(message);
        this.status = status;
        this.data = data;
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.status = HttpStatus.INTERNAL_SERVER_ERROR;
        this.data = null;
    }

    public HttpStatus getStatus() {
        return status;
    }

    public Object getData() {
        return data;
    }
}
