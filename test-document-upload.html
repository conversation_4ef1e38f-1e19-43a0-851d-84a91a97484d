<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档上传测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; white-space: pre-wrap; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
        .warning { background: #fff3e0; color: #f57c00; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; border: none; }
        .btn-success { background: #28a745; color: white; border: none; }
        input[type="file"], input[type="text"] { margin: 10px 0; padding: 5px; width: 300px; }
        .upload-form { border: 2px dashed #ccc; padding: 20px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>DMS文档上传专项测试</h1>
    
    <div class="section">
        <h2>🔐 1. 登录</h2>
        <button class="btn-primary" onclick="testLogin()">登录系统</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="section">
        <h2>📄 2. 文档上传测试</h2>
        <div class="upload-form">
            <h3>选择文件上传</h3>
            <input type="file" id="testFile" accept=".txt,.pdf,.doc,.docx,.jpg,.png">
            <br>
            <input type="text" id="testTitle" placeholder="文档标题" value="测试文档">
            <br>
            <input type="text" id="testDescription" placeholder="文档描述" value="这是一个测试上传的文档">
            <br>
            <button class="btn-success" onclick="testUpload()" id="uploadBtn">
                <span id="uploadText">📤 上传文档</span>
            </button>
        </div>
        <div id="uploadResult" class="result"></div>
    </div>

    <div class="section">
        <h2>📋 3. 上传后验证</h2>
        <button class="btn-primary" onclick="verifyUpload()">验证上传结果</button>
        <div id="verifyResult" class="result"></div>
    </div>

    <div class="section">
        <h2>🔍 4. 调试信息</h2>
        <button class="btn-primary" onclick="showDebugInfo()">显示调试信息</button>
        <div id="debugResult" class="result"></div>
    </div>

    <script>
        let authToken = null;
        let lastUploadedDocId = null;

        function log(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = 'result ' + type;
            console.log(message);
        }

        async function testLogin() {
            try {
                log('loginResult', '正在登录...', 'warning');
                
                const response = await fetch('/dms/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    authToken = result.data.token;
                    localStorage.setItem('token', authToken);
                    log('loginResult', `✅ 登录成功！\n用户: ${result.data.username}\nToken: ${authToken.substring(0, 30)}...`);
                } else {
                    const error = await response.text();
                    log('loginResult', `❌ 登录失败: ${error}`, 'error');
                }
            } catch (error) {
                log('loginResult', `❌ 登录异常: ${error.message}`, 'error');
            }
        }

        async function testUpload() {
            if (!authToken) {
                log('uploadResult', '❌ 请先登录', 'error');
                return;
            }

            const fileInput = document.getElementById('testFile');
            const title = document.getElementById('testTitle').value;
            const description = document.getElementById('testDescription').value;

            if (!fileInput.files[0]) {
                log('uploadResult', '❌ 请选择文件', 'error');
                return;
            }

            const uploadBtn = document.getElementById('uploadBtn');
            const uploadText = document.getElementById('uploadText');
            const originalText = uploadText.innerHTML;

            try {
                // 更新按钮状态
                uploadBtn.disabled = true;
                uploadText.innerHTML = '⏳ 上传中...';
                
                log('uploadResult', '正在上传文档...', 'warning');
                
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                formData.append('title', title);
                formData.append('description', description);

                console.log('=== 开始上传 ===');
                console.log('文件:', fileInput.files[0].name);
                console.log('大小:', fileInput.files[0].size);
                console.log('类型:', fileInput.files[0].type);
                console.log('标题:', title);
                console.log('描述:', description);

                const response = await fetch('/dms/api/documents/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    },
                    body: formData
                });

                console.log('响应状态:', response.status);
                console.log('响应头:', [...response.headers.entries()]);

                if (response.ok) {
                    const result = await response.json();
                    lastUploadedDocId = result.data.id;
                    
                    log('uploadResult', 
                        `✅ 文档上传成功！\n` +
                        `文档ID: ${result.data.id}\n` +
                        `标题: ${result.data.title}\n` +
                        `文件名: ${result.data.originalFileName}\n` +
                        `大小: ${result.data.fileSize} 字节\n` +
                        `状态: ${result.data.status}\n` +
                        `创建时间: ${result.data.createdAt || '未知'}`
                    );
                } else {
                    const errorText = await response.text();
                    console.error('上传失败响应:', errorText);
                    log('uploadResult', `❌ 文档上传失败 (${response.status}): ${errorText}`, 'error');
                }
            } catch (error) {
                console.error('上传异常:', error);
                log('uploadResult', `❌ 文档上传异常: ${error.message}`, 'error');
            } finally {
                // 恢复按钮状态
                uploadBtn.disabled = false;
                uploadText.innerHTML = originalText;
            }
        }

        async function verifyUpload() {
            if (!authToken) {
                log('verifyResult', '❌ 请先登录', 'error');
                return;
            }

            try {
                log('verifyResult', '正在验证上传结果...', 'warning');
                
                // 获取文档列表
                const response = await fetch('/dms/api/documents?page=0&size=10', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const docs = result.data.content;
                    
                    let message = `✅ 验证成功！\n总文档数: ${result.data.totalElements}\n最新文档:\n\n`;
                    
                    docs.slice(0, 3).forEach((doc, index) => {
                        message += `${index + 1}. ID: ${doc.id}\n`;
                        message += `   标题: ${doc.title}\n`;
                        message += `   文件: ${doc.originalFileName}\n`;
                        message += `   大小: ${doc.fileSize} 字节\n`;
                        message += `   状态: ${doc.status}\n`;
                        message += `   创建时间: ${doc.createdAt}\n\n`;
                    });

                    if (lastUploadedDocId) {
                        const uploadedDoc = docs.find(doc => doc.id === lastUploadedDocId);
                        if (uploadedDoc) {
                            message += `🎯 刚上传的文档已确认存在！\n`;
                        } else {
                            message += `⚠️ 刚上传的文档未在列表中找到\n`;
                        }
                    }

                    log('verifyResult', message);
                } else {
                    const error = await response.text();
                    log('verifyResult', `❌ 验证失败: ${error}`, 'error');
                }
            } catch (error) {
                log('verifyResult', `❌ 验证异常: ${error.message}`, 'error');
            }
        }

        async function showDebugInfo() {
            let debugInfo = '=== 调试信息 ===\n\n';
            
            debugInfo += `浏览器: ${navigator.userAgent}\n`;
            debugInfo += `当前时间: ${new Date().toLocaleString()}\n`;
            debugInfo += `Token状态: ${authToken ? '已设置' : '未设置'}\n`;
            debugInfo += `Token长度: ${authToken ? authToken.length : 0}\n`;
            debugInfo += `最后上传文档ID: ${lastUploadedDocId || '无'}\n\n`;

            // 检查本地存储
            debugInfo += '本地存储:\n';
            debugInfo += `- token: ${localStorage.getItem('token') ? '存在' : '不存在'}\n`;
            debugInfo += `- user: ${localStorage.getItem('user') || '不存在'}\n\n`;

            // 检查文件输入
            const fileInput = document.getElementById('testFile');
            if (fileInput.files[0]) {
                const file = fileInput.files[0];
                debugInfo += '选中文件信息:\n';
                debugInfo += `- 名称: ${file.name}\n`;
                debugInfo += `- 大小: ${file.size} 字节\n`;
                debugInfo += `- 类型: ${file.type}\n`;
                debugInfo += `- 最后修改: ${new Date(file.lastModified).toLocaleString()}\n\n`;
            } else {
                debugInfo += '选中文件: 无\n\n';
            }

            // 检查表单数据
            debugInfo += '表单数据:\n';
            debugInfo += `- 标题: ${document.getElementById('testTitle').value}\n`;
            debugInfo += `- 描述: ${document.getElementById('testDescription').value}\n`;

            log('debugResult', debugInfo);
        }

        // 页面加载时尝试从localStorage获取token
        window.onload = function() {
            const savedToken = localStorage.getItem('token');
            if (savedToken) {
                authToken = savedToken;
                log('loginResult', '✅ 已从本地存储加载Token');
            }
        };

        // 文件选择时显示文件信息
        document.getElementById('testFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                console.log('文件选择:', file.name, file.size, file.type);
                // 自动设置标题
                if (!document.getElementById('testTitle').value || document.getElementById('testTitle').value === '测试文档') {
                    document.getElementById('testTitle').value = file.name.replace(/\.[^/.]+$/, "");
                }
            }
        });
    </script>
</body>
</html>
