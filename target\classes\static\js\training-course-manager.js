/**
 * 培训课程管理器
 * 负责培训课程的前端交互逻辑
 */
class TrainingCourseManager {
    constructor() {
        this.currentPage = 0;
        this.pageSize = 10;
        this.currentSort = 'createdAt';
        this.currentDir = 'desc';
        this.isLoading = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        
        this.init();
    }

    init() {
        console.log('=== 培训课程管理器初始化 ===');
        this.bindEvents();
        this.loadCourses();
        this.loadCourseStats();
    }

    bindEvents() {
        // 搜索表单事件
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.currentPage = 0;
                this.loadCourses();
            });
        }

        // 创建课程表单事件
        const createForm = document.getElementById('createCourseForm');
        if (createForm) {
            createForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.createCourse();
            });
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshCourses());
        }

        // 文件上传处理
        const fileInput = document.getElementById('trainingDocument');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }
    }

    // 加载课程列表
    async loadCourses() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoadingState();

        try {
            console.log('=== 加载培训课程列表 ===');
            
            const searchParams = new URLSearchParams({
                page: this.currentPage,
                size: this.pageSize,
                sortBy: this.currentSort,
                sortDir: this.currentDir
            });

            // 添加搜索条件
            this.addSearchParams(searchParams);

            const response = await authUtils.secureApiCall(`/dms/api/training/courses?${searchParams}`);

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 课程加载成功:', result);
                
                this.displayCourses(result.data.content || result.data);
                this.updatePagination(result.data);
                this.retryCount = 0;
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('❌ 加载课程失败:', error);
            this.handleLoadError(error);
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
        }
    }

    // 添加搜索参数
    addSearchParams(searchParams) {
        const title = document.getElementById('searchTitle')?.value;
        const courseCode = document.getElementById('searchCourseCode')?.value;
        const courseType = document.getElementById('searchType')?.value;
        const status = document.getElementById('searchStatus')?.value;
        const instructor = document.getElementById('searchInstructor')?.value;

        if (title) searchParams.append('title', title);
        if (courseCode) searchParams.append('courseCode', courseCode);
        if (courseType) searchParams.append('courseType', courseType);
        if (status) searchParams.append('status', status);
        if (instructor) searchParams.append('instructorId', instructor);
    }

    // 显示课程列表
    displayCourses(courses) {
        console.log('=== 显示课程列表 ===');
        console.log('课程数量:', courses ? courses.length : 0);

        const container = document.getElementById('coursesContainer');
        if (!container) {
            console.error('❌ 找不到课程容器元素');
            return;
        }

        if (!courses || courses.length === 0) {
            container.innerHTML = this.getEmptyStateHtml();
            return;
        }

        const coursesHtml = courses.map(course => this.createCourseCard(course)).join('');
        container.innerHTML = coursesHtml;

        console.log('✅ 课程列表显示完成');
    }

    // 创建课程卡片
    createCourseCard(course) {
        const statusBadge = this.getStatusBadge(course.status);
        const typeBadge = this.getTypeBadge(course.courseType);
        const difficultyBadge = this.getDifficultyBadge(course.difficultyLevel);
        
        return `
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 shadow-sm course-card" data-course-id="${course.id}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            ${statusBadge}
                            ${typeBadge}
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="trainingCourseManager.viewCourse(${course.id})">
                                    <i class="fas fa-eye me-2"></i>查看详情</a></li>
                                <li><a class="dropdown-item" href="#" onclick="trainingCourseManager.editCourse(${course.id})">
                                    <i class="fas fa-edit me-2"></i>编辑课程</a></li>
                                <li><a class="dropdown-item" href="#" onclick="trainingCourseManager.assignCourse(${course.id})">
                                    <i class="fas fa-users me-2"></i>分配培训</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="trainingCourseManager.deleteCourse(${course.id})">
                                    <i class="fas fa-trash me-2"></i>删除课程</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">${this.escapeHtml(course.title)}</h5>
                        <p class="card-text text-muted small mb-2">
                            <i class="fas fa-code me-1"></i>${course.courseCode}
                        </p>
                        <p class="card-text">${this.escapeHtml(course.description || '暂无描述')}</p>
                        
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <small class="text-muted">时长</small>
                                <div class="fw-bold">${course.durationMinutes || 0}分钟</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">及格分</small>
                                <div class="fw-bold">${course.passingScore || 80}分</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">难度</small>
                                <div>${difficultyBadge}</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                ${course.instructor ? course.instructor.firstName + ' ' + course.instructor.lastName : '未指定'}
                            </small>
                            <small class="text-muted">
                                ${this.formatDate(course.createdAt)}
                            </small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="trainingCourseManager.startLearning(${course.id})">
                                <i class="fas fa-play me-1"></i>开始学习
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="trainingCourseManager.viewProgress(${course.id})">
                                <i class="fas fa-chart-line me-1"></i>查看进度
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 创建课程
    async createCourse() {
        try {
            console.log('=== 开始创建课程 ===');

            const formData = this.getFormData();
            console.log('表单数据:', formData);

            // 验证必填字段
            if (!formData.title || !formData.courseCode) {
                this.showNotification('请填写课程标题和课程代码', 'error');
                return;
            }

            const response = await authUtils.secureApiCall('/dms/api/training/courses', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 课程创建成功:', result);
                
                this.showNotification('课程创建成功！', 'success');
                this.closeCreateModal();
                this.resetCreateForm();
                this.loadCourses();
                this.loadCourseStats();
            } else {
                const error = await response.json();
                throw new Error(error.message || '创建失败');
            }
        } catch (error) {
            console.error('❌ 创建课程失败:', error);
            this.showNotification('创建课程失败: ' + error.message, 'error');
        }
    }

    // 获取表单数据
    getFormData() {
        return {
            title: document.getElementById('courseTitle')?.value?.trim(),
            courseCode: document.getElementById('courseCode')?.value?.trim(),
            description: document.getElementById('courseDescription')?.value?.trim(),
            courseType: document.getElementById('courseType')?.value || 'GENERAL',
            contentType: document.getElementById('contentType')?.value || 'MIXED',
            difficultyLevel: document.getElementById('difficultyLevel')?.value || 'BEGINNER',
            durationMinutes: parseInt(document.getElementById('durationMinutes')?.value) || 60,
            passingScore: parseInt(document.getElementById('passingScore')?.value) || 80,
            maxAttempts: parseInt(document.getElementById('maxAttempts')?.value) || 3,
            estimatedHours: parseFloat(document.getElementById('estimatedHours')?.value) || null,
            isMandatory: document.getElementById('isMandatory')?.checked || false,
            autoAssign: document.getElementById('autoAssign')?.checked || false,
            videoUrl: document.getElementById('videoUrl')?.value?.trim(),
            externalLink: document.getElementById('externalLink')?.value?.trim(),
            learningObjectives: document.getElementById('learningObjectives')?.value?.trim(),
            prerequisites: document.getElementById('prerequisites')?.value?.trim()
        };
    }

    // 工具方法
    getStatusBadge(status) {
        const badges = {
            'DRAFT': '<span class="badge bg-secondary">草稿</span>',
            'UNDER_REVIEW': '<span class="badge bg-warning">审核中</span>',
            'APPROVED': '<span class="badge bg-info">已批准</span>',
            'ACTIVE': '<span class="badge bg-success">活跃</span>',
            'INACTIVE': '<span class="badge bg-dark">非活跃</span>',
            'ARCHIVED': '<span class="badge bg-secondary">已归档</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">未知</span>';
    }

    getTypeBadge(type) {
        const badges = {
            'GENERAL': '<span class="badge bg-primary">通用</span>',
            'GMP': '<span class="badge bg-danger">GMP</span>',
            'SOP': '<span class="badge bg-info">SOP</span>',
            'SAFETY': '<span class="badge bg-warning">安全</span>',
            'QUALITY': '<span class="badge bg-success">质量</span>',
            'REGULATORY': '<span class="badge bg-purple">法规</span>',
            'TECHNICAL': '<span class="badge bg-dark">技术</span>',
            'COMPLIANCE': '<span class="badge bg-orange">合规</span>'
        };
        return badges[type] || '<span class="badge bg-secondary">其他</span>';
    }

    getDifficultyBadge(level) {
        const badges = {
            'BEGINNER': '<span class="badge bg-success">初级</span>',
            'INTERMEDIATE': '<span class="badge bg-warning">中级</span>',
            'ADVANCED': '<span class="badge bg-danger">高级</span>',
            'EXPERT': '<span class="badge bg-dark">专家</span>'
        };
        return badges[level] || '<span class="badge bg-secondary">未知</span>';
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }

    // 占位方法，将在后续实现
    async loadCourseStats() {
        // TODO: 实现统计数据加载
    }

    showLoadingState() {
        // TODO: 实现加载状态显示
    }

    hideLoadingState() {
        // TODO: 实现加载状态隐藏
    }

    handleLoadError(error) {
        // TODO: 实现错误处理
    }

    updatePagination(pageData) {
        // TODO: 实现分页更新
    }

    getEmptyStateHtml() {
        return `
            <div class="text-center py-5">
                <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无培训课程</h5>
                <p class="text-muted">点击"创建课程"按钮开始创建您的第一个培训课程</p>
            </div>
        `;
    }

    showNotification(message, type) {
        // TODO: 实现通知显示
        console.log(`${type.toUpperCase()}: ${message}`);
    }

    closeCreateModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('createCourseModal'));
        if (modal) modal.hide();
    }

    resetCreateForm() {
        document.getElementById('createCourseForm')?.reset();
    }

    refreshCourses() {
        this.currentPage = 0;
        this.loadCourses();
    }

    // 课程操作方法（占位）
    async viewCourse(id) { console.log('查看课程:', id); }
    async editCourse(id) { console.log('编辑课程:', id); }
    async assignCourse(id) { console.log('分配课程:', id); }
    async deleteCourse(id) { console.log('删除课程:', id); }
    async startLearning(id) { console.log('开始学习:', id); }
    async viewProgress(id) { console.log('查看进度:', id); }
    async handleFileUpload(event) { console.log('文件上传:', event); }
}

// 全局实例
let trainingCourseManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    trainingCourseManager = new TrainingCourseManager();
});
