<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API响应诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .response-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .response-headers {
            background: #e9ecef;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .response-body {
            background: #ffffff;
            border: 1px solid #ced4da;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 API响应诊断工具</h1>
    
    <div class="section">
        <h3>认证状态</h3>
        <button class="btn-primary" onclick="checkAuth()">检查认证</button>
        <div id="authStatus"></div>
    </div>

    <div class="section">
        <h3>API测试</h3>
        <button class="btn-success" onclick="testAPI('/dms/api/test/basic')">测试基础API</button>
        <button class="btn-warning" onclick="testAPI('/dms/api/dashboard/stats')">测试仪表板API</button>
        <button class="btn-warning" onclick="testAPI('/dms/api/users')">测试用户API</button>
        <button class="btn-warning" onclick="testAPI('/dms/api/documents')">测试文档API</button>
        <button class="btn-danger" onclick="clearLog()">清空日志</button>
    </div>

    <div class="section">
        <h3>文件上传测试</h3>
        <input type="file" id="testFile" accept=".pdf,.doc,.docx,.txt">
        <input type="text" id="testTitle" placeholder="文档标题" value="测试文档">
        <button class="btn-success" onclick="testFileUpload()">测试文件上传</button>
    </div>

    <div class="section">
        <h3>原始响应分析</h3>
        <div id="responseAnalysis"></div>
    </div>

    <div class="section">
        <h3>诊断日志</h3>
        <div id="diagnosisLog" class="log"></div>
    </div>

    <script src="/dms/js/auth.js"></script>
    <script>
        let logElement = document.getElementById('diagnosisLog');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            logElement.innerHTML = '';
            document.getElementById('responseAnalysis').innerHTML = '';
        }

        function checkAuth() {
            log('=== 检查认证状态 ===', 'info');
            
            try {
                authUtils.initAuth();
                
                if (authUtils.isLoggedIn()) {
                    const user = authUtils.getCurrentUser();
                    log(`✅ 用户已登录: ${user.username}`, 'success');
                    log(`   - 角色: ${user.roles.join(', ')}`, 'info');
                    
                    const token = localStorage.getItem('token');
                    log(`   - Token长度: ${token ? token.length : 0}`, 'info');
                    log(`   - Token前缀: ${token ? token.substring(0, 50) + '...' : 'N/A'}`, 'info');
                    
                    document.getElementById('authStatus').innerHTML = 
                        `<span class="success">✅ 已登录: ${user.username}</span>`;
                } else {
                    log('❌ 用户未登录', 'error');
                    document.getElementById('authStatus').innerHTML = 
                        '<span class="error">❌ 未登录</span>';
                }
            } catch (error) {
                log(`❌ 认证检查失败: ${error.message}`, 'error');
            }
        }

        async function testAPI(url) {
            log(`=== 测试API: ${url} ===`, 'info');
            
            if (!authUtils.isLoggedIn()) {
                log('❌ 用户未登录，无法测试API', 'error');
                return;
            }

            const token = localStorage.getItem('token');
            
            try {
                log('🌐 发送原始fetch请求...', 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });

                log(`📨 响应状态: ${response.status} ${response.statusText}`, 'info');
                log(`📨 响应类型: ${response.type}`, 'info');
                log(`📨 响应URL: ${response.url}`, 'info');
                log(`📨 响应重定向: ${response.redirected}`, 'info');

                // 获取响应头
                const headers = {};
                for (let [key, value] of response.headers.entries()) {
                    headers[key] = value;
                }
                log(`📨 响应头: ${JSON.stringify(headers, null, 2)}`, 'info');

                // 尝试获取原始响应文本
                const responseClone = response.clone();
                let responseText = '';
                try {
                    responseText = await responseClone.text();
                    log(`📨 原始响应长度: ${responseText.length}`, 'info');
                    log(`📨 原始响应内容: ${responseText.substring(0, 500)}${responseText.length > 500 ? '...' : ''}`, 'info');
                } catch (textError) {
                    log(`❌ 无法读取响应文本: ${textError.message}`, 'error');
                }

                // 显示响应分析
                displayResponseAnalysis(url, response.status, headers, responseText);

                // 尝试解析JSON
                if (response.ok) {
                    try {
                        const jsonData = await response.json();
                        log(`✅ JSON解析成功`, 'success');
                        log(`📊 JSON数据: ${JSON.stringify(jsonData, null, 2)}`, 'success');
                    } catch (jsonError) {
                        log(`❌ JSON解析失败: ${jsonError.message}`, 'error');
                        log(`🔍 这是导致"Unexpected end of JSON input"错误的原因`, 'error');
                    }
                } else {
                    log(`❌ HTTP错误: ${response.status}`, 'error');
                }

            } catch (error) {
                log(`💥 请求异常: ${error.message}`, 'error');
                log(`🔍 错误类型: ${error.constructor.name}`, 'error');
                log(`🔍 错误堆栈: ${error.stack}`, 'error');
            }
        }

        function displayResponseAnalysis(url, status, headers, body) {
            const analysisDiv = document.getElementById('responseAnalysis');
            
            const analysisHTML = `
                <div class="response-box">
                    <h4>📊 响应分析: ${url}</h4>
                    <p><strong>状态码:</strong> ${status}</p>
                    
                    <h5>响应头:</h5>
                    <div class="response-headers">${JSON.stringify(headers, null, 2)}</div>
                    
                    <h5>响应体 (前1000字符):</h5>
                    <div class="response-body">${body.substring(0, 1000)}${body.length > 1000 ? '\n...(截断)' : ''}</div>
                    
                    <h5>诊断结果:</h5>
                    <ul>
                        <li>响应长度: ${body.length} 字符</li>
                        <li>Content-Type: ${headers['content-type'] || '未设置'}</li>
                        <li>是否为空响应: ${body.length === 0 ? '是' : '否'}</li>
                        <li>是否为有效JSON: ${isValidJSON(body) ? '是' : '否'}</li>
                        <li>可能的问题: ${diagnoseProblem(status, headers, body)}</li>
                    </ul>
                </div>
            `;
            
            analysisDiv.innerHTML += analysisHTML;
        }

        function isValidJSON(str) {
            try {
                JSON.parse(str);
                return true;
            } catch (e) {
                return false;
            }
        }

        function diagnoseProblem(status, headers, body) {
            const problems = [];
            
            if (body.length === 0) {
                problems.push('响应体为空');
            }
            
            if (!headers['content-type'] || !headers['content-type'].includes('application/json')) {
                problems.push('Content-Type不是application/json');
            }
            
            if (status !== 200) {
                problems.push(`HTTP状态码异常: ${status}`);
            }
            
            if (body.length > 0 && !isValidJSON(body)) {
                problems.push('响应内容不是有效的JSON格式');
            }
            
            if (body.includes('<html>') || body.includes('<!DOCTYPE')) {
                problems.push('响应返回了HTML页面而不是JSON');
            }
            
            return problems.length > 0 ? problems.join('; ') : '未发现明显问题';
        }

        async function testFileUpload() {
            log('=== 测试文件上传 ===', 'info');
            
            const fileInput = document.getElementById('testFile');
            const titleInput = document.getElementById('testTitle');
            
            if (!fileInput.files[0]) {
                log('❌ 请选择文件', 'error');
                return;
            }
            
            if (!titleInput.value.trim()) {
                log('❌ 请输入标题', 'error');
                return;
            }
            
            const file = fileInput.files[0];
            const title = titleInput.value.trim();
            
            log(`📁 文件信息: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`, 'info');
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('title', title);
            formData.append('description', '测试上传');
            
            const token = localStorage.getItem('token');
            
            try {
                log('🌐 发送文件上传请求...', 'info');
                
                const response = await fetch('/dms/api/documents/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + token
                        // 不设置Content-Type，让浏览器自动设置multipart/form-data
                    },
                    body: formData
                });

                log(`📨 上传响应状态: ${response.status} ${response.statusText}`, 'info');

                // 获取响应头
                const headers = {};
                for (let [key, value] of response.headers.entries()) {
                    headers[key] = value;
                }
                log(`📨 上传响应头: ${JSON.stringify(headers, null, 2)}`, 'info');

                // 获取原始响应
                const responseText = await response.text();
                log(`📨 上传原始响应: ${responseText}`, 'info');

                // 显示分析
                displayResponseAnalysis('/dms/api/documents/upload', response.status, headers, responseText);

                if (response.ok && responseText) {
                    try {
                        const result = JSON.parse(responseText);
                        log(`✅ 文件上传成功: ${JSON.stringify(result, null, 2)}`, 'success');
                    } catch (jsonError) {
                        log(`❌ 上传响应JSON解析失败: ${jsonError.message}`, 'error');
                    }
                } else {
                    log(`❌ 文件上传失败`, 'error');
                }

            } catch (error) {
                log(`💥 文件上传异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查认证
        window.addEventListener('load', function() {
            log('🚀 API诊断工具加载完成', 'info');
            checkAuth();
        });
    </script>
</body>
</html>
