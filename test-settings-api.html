<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; white-space: pre-wrap; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>设置API测试</h1>
    
    <div class="section">
        <h2>1. 登录测试</h2>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="section">
        <h2>2. 测试不同的设置API路径</h2>
        <button onclick="testAPI('/dms/api/settings/config')">测试 /dms/api/settings/config</button>
        <button onclick="testAPI('/api/settings/config')">测试 /api/settings/config</button>
        <button onclick="testAPI('/dms/api/settings/system')">测试 /dms/api/settings/system</button>
        <button onclick="testAPI('/api/settings/system')">测试 /api/settings/system</button>
        <div id="apiResult" class="result"></div>
    </div>

    <div class="section">
        <h2>3. 检查所有可能的路径</h2>
        <button onclick="testAllPaths()">测试所有路径</button>
        <div id="allPathsResult" class="result"></div>
    </div>

    <script>
        let authToken = null;

        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = 'result ' + (isError ? 'error' : 'success');
            console.log(message);
        }

        async function testLogin() {
            try {
                const response = await fetch('/dms/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    authToken = result.data.token;
                    localStorage.setItem('token', authToken);
                    log('loginResult', '✅ 登录成功！Token: ' + authToken.substring(0, 20) + '...');
                } else {
                    const error = await response.text();
                    log('loginResult', '❌ 登录失败: ' + error, true);
                }
            } catch (error) {
                log('loginResult', '❌ 登录异常: ' + error.message, true);
            }
        }

        async function testAPI(path) {
            if (!authToken) {
                log('apiResult', '❌ 请先登录', true);
                return;
            }

            try {
                console.log('测试路径:', path);
                const response = await fetch(path, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + authToken,
                        'Content-Type': 'application/json'
                    }
                });

                console.log('响应状态:', response.status);
                console.log('响应头:', [...response.headers.entries()]);

                if (response.ok) {
                    const result = await response.json();
                    log('apiResult', `✅ ${path} 成功！\n状态: ${response.status}\n数据: ${JSON.stringify(result, null, 2)}`);
                } else {
                    const error = await response.text();
                    log('apiResult', `❌ ${path} 失败！\n状态: ${response.status}\n错误: ${error}`, true);
                }
            } catch (error) {
                log('apiResult', `❌ ${path} 异常: ${error.message}`, true);
            }
        }

        async function testAllPaths() {
            if (!authToken) {
                log('allPathsResult', '❌ 请先登录', true);
                return;
            }

            const paths = [
                '/dms/api/settings/config',
                '/api/settings/config',
                '/dms/api/settings/system',
                '/api/settings/system',
                '/dms/api/settings/security',
                '/api/settings/security',
                '/dms/api/settings/features',
                '/api/settings/features'
            ];

            let results = '测试所有设置API路径:\n\n';

            for (const path of paths) {
                try {
                    console.log('测试路径:', path);
                    const response = await fetch(path, {
                        method: 'GET',
                        headers: {
                            'Authorization': 'Bearer ' + authToken,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        results += `✅ ${path} - 状态: ${response.status} - 成功\n`;
                    } else {
                        results += `❌ ${path} - 状态: ${response.status} - 失败\n`;
                    }
                } catch (error) {
                    results += `❌ ${path} - 异常: ${error.message}\n`;
                }
            }

            log('allPathsResult', results);
        }

        // 页面加载时尝试从localStorage获取token
        window.onload = function() {
            const savedToken = localStorage.getItem('token');
            if (savedToken) {
                authToken = savedToken;
                log('loginResult', '✅ 已从本地存储加载Token');
            }
        };
    </script>
</body>
</html>
