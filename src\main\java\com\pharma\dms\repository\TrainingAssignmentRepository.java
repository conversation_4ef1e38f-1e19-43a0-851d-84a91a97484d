package com.pharma.dms.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.pharma.dms.entity.TrainingAssignment;
import com.pharma.dms.entity.TrainingCourse;
import com.pharma.dms.entity.User;

@Repository
public interface TrainingAssignmentRepository extends JpaRepository<TrainingAssignment, Long> {

    // 基本查询
    List<TrainingAssignment> findByUser(User user);
    
    List<TrainingAssignment> findByCourse(TrainingCourse course);
    
    List<TrainingAssignment> findByAssignedBy(User assignedBy);
    
    Optional<TrainingAssignment> findByCourseAndUser(TrainingCourse course, User user);

    // 状态查询
    List<TrainingAssignment> findByStatus(TrainingAssignment.AssignmentStatus status);
    
    List<TrainingAssignment> findByUserAndStatus(User user, TrainingAssignment.AssignmentStatus status);
    
    List<TrainingAssignment> findByCourseAndStatus(TrainingCourse course, TrainingAssignment.AssignmentStatus status);

    // 分页查询
    Page<TrainingAssignment> findByUser(User user, Pageable pageable);
    
    Page<TrainingAssignment> findByCourse(TrainingCourse course, Pageable pageable);
    
    Page<TrainingAssignment> findByStatus(TrainingAssignment.AssignmentStatus status, Pageable pageable);

    // 日期查询
    List<TrainingAssignment> findByDueDateBefore(LocalDateTime date);
    
    List<TrainingAssignment> findByDueDateBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<TrainingAssignment> findByAssignedDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    // 逾期查询
    @Query("SELECT ta FROM TrainingAssignment ta WHERE ta.dueDate < :now AND ta.status != 'COMPLETED'")
    List<TrainingAssignment> findOverdueAssignments(@Param("now") LocalDateTime now);

    // 需要提醒的分配
    @Query("SELECT ta FROM TrainingAssignment ta WHERE ta.dueDate BETWEEN :now AND :reminderTime " +
           "AND ta.status IN ('ASSIGNED', 'IN_PROGRESS') AND ta.reminderSent = false")
    List<TrainingAssignment> findAssignmentsNeedingReminder(@Param("now") LocalDateTime now, 
                                                           @Param("reminderTime") LocalDateTime reminderTime);

    // 统计查询
    @Query("SELECT COUNT(ta) FROM TrainingAssignment ta WHERE ta.user = :user")
    Long countByUser(@Param("user") User user);
    
    @Query("SELECT COUNT(ta) FROM TrainingAssignment ta WHERE ta.course = :course")
    Long countByCourse(@Param("course") TrainingCourse course);
    
    @Query("SELECT COUNT(ta) FROM TrainingAssignment ta WHERE ta.user = :user AND ta.status = :status")
    Long countByUserAndStatus(@Param("user") User user, 
                             @Param("status") TrainingAssignment.AssignmentStatus status);

    // 完成率查询
    @Query("SELECT COUNT(ta) FROM TrainingAssignment ta WHERE ta.course = :course AND ta.status = 'COMPLETED'")
    Long countCompletedByCourse(@Param("course") TrainingCourse course);

    // 用户培训历史
    @Query("SELECT ta FROM TrainingAssignment ta WHERE ta.user = :user ORDER BY ta.assignedDate DESC")
    List<TrainingAssignment> findUserTrainingHistory(@Param("user") User user, Pageable pageable);

    // 课程分配历史
    @Query("SELECT ta FROM TrainingAssignment ta WHERE ta.course = :course ORDER BY ta.assignedDate DESC")
    List<TrainingAssignment> findCourseAssignmentHistory(@Param("course") TrainingCourse course, Pageable pageable);

    // 部门培训查询
    @Query("SELECT ta FROM TrainingAssignment ta WHERE ta.user.department.id = :departmentId")
    List<TrainingAssignment> findByDepartment(@Param("departmentId") Long departmentId);
    
    @Query("SELECT COUNT(ta) FROM TrainingAssignment ta WHERE ta.user.department.id = :departmentId " +
           "AND ta.status = 'COMPLETED'")
    Long countCompletedByDepartment(@Param("departmentId") Long departmentId);

    // 高级查询
    @Query("SELECT ta FROM TrainingAssignment ta WHERE " +
           "(:courseId IS NULL OR ta.course.id = :courseId) AND " +
           "(:userId IS NULL OR ta.user.id = :userId) AND " +
           "(:status IS NULL OR ta.status = :status) AND " +
           "(:startDate IS NULL OR ta.assignedDate >= :startDate) AND " +
           "(:endDate IS NULL OR ta.assignedDate <= :endDate)")
    Page<TrainingAssignment> findAssignmentsWithFilters(@Param("courseId") Long courseId,
                                                       @Param("userId") Long userId,
                                                       @Param("status") TrainingAssignment.AssignmentStatus status,
                                                       @Param("startDate") LocalDateTime startDate,
                                                       @Param("endDate") LocalDateTime endDate,
                                                       Pageable pageable);

    // 批量操作查询
    @Query("SELECT ta FROM TrainingAssignment ta WHERE ta.course.id IN :courseIds AND ta.user.id = :userId")
    List<TrainingAssignment> findByCoursesAndUser(@Param("courseIds") List<Long> courseIds, 
                                                 @Param("userId") Long userId);

    // 批量操作查询
    List<TrainingAssignment> findByBatchId(String batchId);

    @Query("SELECT ta FROM TrainingAssignment ta WHERE ta.batchId = :batchId ORDER BY ta.assignedDate DESC")
    List<TrainingAssignment> findByBatchIdOrderByAssignedDateDesc(@Param("batchId") String batchId);

    // 删除操作
    void deleteByCourse(TrainingCourse course);

    void deleteByUser(User user);

    @Query("DELETE FROM TrainingAssignment ta WHERE ta.course = :course AND ta.user = :user")
    void deleteByCourseAndUser(@Param("course") TrainingCourse course, @Param("user") User user);
}
