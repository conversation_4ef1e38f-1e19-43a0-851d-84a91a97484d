<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }
        .activity-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
            margin: 5px 0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🧪 仪表板API测试</h1>
    
    <div class="test-section">
        <h3>1. 认证状态检查</h3>
        <button class="btn-primary" onclick="checkAuth()">检查认证状态</button>
        <div id="authStatus"></div>
    </div>

    <div class="test-section">
        <h3>2. API测试</h3>
        <button class="btn-success" onclick="testDashboardStats()">测试仪表板统计API</button>
        <button class="btn-warning" onclick="testRecentActivity()">测试最近活动API</button>
        <button class="btn-primary" onclick="testSystemStatus()">测试系统状态API</button>
        <button class="btn-danger" onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-section">
        <h3>3. 仪表板统计数据</h3>
        <div id="statsContainer">
            <div class="stats-grid" id="statsGrid" style="display: none;">
                <div class="stat-card">
                    <div class="stat-value" id="totalDocuments">-</div>
                    <div class="stat-label">总文档数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="activeUsers">-</div>
                    <div class="stat-label">活跃用户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="pendingReviews">-</div>
                    <div class="stat-label">待审核</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="systemAlerts">-</div>
                    <div class="stat-label">系统警告</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="auditLogsToday">-</div>
                    <div class="stat-label">今日审计日志</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalUsers">-</div>
                    <div class="stat-label">总用户数</div>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>4. 最近活动</h3>
        <div id="recentActivityContainer">
            <div id="recentActivityList"></div>
        </div>
    </div>

    <div class="test-section">
        <h3>5. 测试日志</h3>
        <div id="testLog" class="log"></div>
    </div>

    <script src="/dms/js/auth.js"></script>
    <script>
        let logElement = document.getElementById('testLog');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            logElement.innerHTML = '';
        }

        async function checkAuth() {
            log('=== 检查认证状态 ===', 'info');
            
            try {
                authUtils.initAuth();
                
                if (authUtils.isLoggedIn()) {
                    const user = authUtils.getCurrentUser();
                    log(`✅ 用户已登录: ${user.username}`, 'success');
                    log(`   - 角色: ${user.roles.join(', ')}`, 'info');
                    
                    document.getElementById('authStatus').innerHTML = 
                        `<span class="success">✅ 已登录: ${user.username}</span>`;
                } else {
                    log('❌ 用户未登录', 'error');
                    document.getElementById('authStatus').innerHTML = 
                        '<span class="error">❌ 未登录</span>';
                }
            } catch (error) {
                log(`❌ 认证检查失败: ${error.message}`, 'error');
            }
        }

        async function testDashboardStats() {
            log('=== 测试仪表板统计API ===', 'info');
            
            if (!authUtils.isLoggedIn()) {
                log('❌ 用户未登录，无法测试API', 'error');
                return;
            }
            
            try {
                log('🌐 调用 /dms/api/dashboard/stats...', 'info');
                const response = await authUtils.secureApiCall('/dms/api/dashboard/stats');
                
                log(`📨 响应状态: ${response ? response.status : 'null'}`, 'info');
                
                if (response && response.ok) {
                    const result = await response.json();
                    log('✅ 仪表板统计API调用成功', 'success');
                    log(`   - 响应结构: ${JSON.stringify(Object.keys(result), null, 2)}`, 'info');
                    
                    if (result.data) {
                        log(`   - 统计数据: ${JSON.stringify(result.data, null, 2)}`, 'info');
                        displayStats(result.data);
                    }
                } else if (response) {
                    const errorData = await response.json();
                    log(`❌ 仪表板统计API失败: ${errorData.message}`, 'error');
                } else {
                    log('❌ 网络连接失败', 'error');
                }
            } catch (error) {
                log(`💥 仪表板统计API异常: ${error.message}`, 'error');
            }
        }

        async function testRecentActivity() {
            log('=== 测试最近活动API ===', 'info');
            
            try {
                log('🌐 调用 /dms/api/dashboard/recent-activity...', 'info');
                const response = await authUtils.secureApiCall('/dms/api/dashboard/recent-activity');
                
                if (response && response.ok) {
                    const result = await response.json();
                    log('✅ 最近活动API调用成功', 'success');
                    log(`   - 活动数量: ${result.data.recentLogs ? result.data.recentLogs.length : 0}`, 'info');
                    
                    if (result.data.recentLogs) {
                        displayRecentActivity(result.data.recentLogs);
                    }
                } else if (response) {
                    const errorData = await response.json();
                    log(`❌ 最近活动API失败: ${errorData.message}`, 'error');
                } else {
                    log('❌ 网络连接失败', 'error');
                }
            } catch (error) {
                log(`💥 最近活动API异常: ${error.message}`, 'error');
            }
        }

        async function testSystemStatus() {
            log('=== 测试系统状态API ===', 'info');
            
            try {
                log('🌐 调用 /dms/api/dashboard/system-status...', 'info');
                const response = await authUtils.secureApiCall('/dms/api/dashboard/system-status');
                
                if (response && response.ok) {
                    const result = await response.json();
                    log('✅ 系统状态API调用成功', 'success');
                    log(`   - 系统状态: ${JSON.stringify(result.data, null, 2)}`, 'info');
                } else if (response) {
                    const errorData = await response.json();
                    log(`❌ 系统状态API失败: ${errorData.message}`, 'error');
                } else {
                    log('❌ 网络连接失败', 'error');
                }
            } catch (error) {
                log(`💥 系统状态API异常: ${error.message}`, 'error');
            }
        }

        function displayStats(stats) {
            document.getElementById('statsGrid').style.display = 'grid';
            
            document.getElementById('totalDocuments').textContent = stats.totalDocuments || '0';
            document.getElementById('activeUsers').textContent = stats.activeUsers || '0';
            document.getElementById('pendingReviews').textContent = stats.pendingReviews || '0';
            document.getElementById('systemAlerts').textContent = stats.systemAlerts || '0';
            document.getElementById('auditLogsToday').textContent = stats.auditLogsToday || '0';
            document.getElementById('totalUsers').textContent = stats.totalUsers || '0';
            
            log(`✅ 显示统计数据完成`, 'success');
        }

        function displayRecentActivity(activities) {
            const container = document.getElementById('recentActivityList');
            
            if (!Array.isArray(activities) || activities.length === 0) {
                container.innerHTML = '<div class="activity-item">暂无最近活动</div>';
                return;
            }
            
            container.innerHTML = '';
            
            activities.forEach(activity => {
                const item = document.createElement('div');
                item.className = 'activity-item';
                item.innerHTML = `
                    <strong>${activity.action || '未知操作'}</strong><br>
                    <small>${activity.description || '无描述'}</small><br>
                    <small style="color: #6c757d;">${activity.timestamp || '未知时间'} - ${activity.username || '未知用户'}</small>
                `;
                container.appendChild(item);
            });
            
            log(`✅ 显示了 ${activities.length} 条最近活动`, 'success');
        }

        // 页面加载时自动检查认证状态
        window.addEventListener('load', function() {
            log('🚀 页面加载完成，开始测试...', 'info');
            checkAuth();
        });
    </script>
</body>
</html>
