package com.pharma.dms.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Entity
@Table(name = "training_progress")
public class TrainingProgress extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "training_record_id", nullable = false)
    private TrainingRecord trainingRecord;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "module_id")
    private TrainingModule module;

    @Column(name = "progress_percentage", nullable = false)
    private Integer progressPercentage = 0;

    @Column(name = "time_spent_minutes", nullable = false)
    private Integer timeSpentMinutes = 0;

    @Column(name = "last_accessed")
    private LocalDateTime lastAccessed;

    @Column(name = "is_completed", nullable = false)
    private Boolean isCompleted = false;

    @Column(name = "completion_date")
    private LocalDateTime completionDate;

    @Column(name = "bookmark_position")
    private String bookmarkPosition;

    @Column(name = "notes")
    private String notes;

    // 新增字段：详细学习跟踪
    @Column(name = "session_id")
    private String sessionId; // 学习会话ID

    @Column(name = "start_time")
    private LocalDateTime startTime; // 本次学习开始时间

    @Column(name = "end_time")
    private LocalDateTime endTime; // 本次学习结束时间

    @Column(name = "pause_count")
    private Integer pauseCount = 0; // 暂停次数

    @Column(name = "total_pause_time_minutes")
    private Integer totalPauseTimeMinutes = 0; // 总暂停时间

    @Column(name = "interaction_count")
    private Integer interactionCount = 0; // 交互次数（点击、滚动等）

    @Column(name = "quiz_attempts")
    private Integer quizAttempts = 0; // 章节测验尝试次数

    @Column(name = "quiz_score")
    private Integer quizScore; // 章节测验得分

    @Column(name = "difficulty_rating")
    private Integer difficultyRating; // 用户评价的难度等级 (1-5)

    @Column(name = "satisfaction_rating")
    private Integer satisfactionRating; // 用户满意度评级 (1-5)

    @Column(name = "learning_path")
    private String learningPath; // 学习路径记录 (JSON格式)

    @Column(name = "device_type")
    private String deviceType; // 学习设备类型

    @Column(name = "browser_info")
    private String browserInfo; // 浏览器信息

    @Column(name = "ip_address")
    private String ipAddress; // 学习时的IP地址

    @Column(name = "is_mobile_device")
    private Boolean isMobileDevice = false; // 是否移动设备

    @Column(name = "video_watch_time_seconds")
    private Integer videoWatchTimeSeconds = 0; // 视频观看时长

    @Column(name = "document_read_time_seconds")
    private Integer documentReadTimeSeconds = 0; // 文档阅读时长

    @Column(name = "last_bookmark_time")
    private LocalDateTime lastBookmarkTime; // 最后书签时间

    @Column(name = "auto_saved")
    private Boolean autoSaved = false; // 是否自动保存进度

    // Constructors
    public TrainingProgress() {}

    public TrainingProgress(TrainingRecord trainingRecord, TrainingModule module) {
        this.trainingRecord = trainingRecord;
        this.module = module;
        this.lastAccessed = LocalDateTime.now();
    }

    // Getters and Setters
    public TrainingRecord getTrainingRecord() {
        return trainingRecord;
    }

    public void setTrainingRecord(TrainingRecord trainingRecord) {
        this.trainingRecord = trainingRecord;
    }

    public TrainingModule getModule() {
        return module;
    }

    public void setModule(TrainingModule module) {
        this.module = module;
    }

    public Integer getProgressPercentage() {
        return progressPercentage;
    }

    public void setProgressPercentage(Integer progressPercentage) {
        this.progressPercentage = progressPercentage;
    }

    public Integer getTimeSpentMinutes() {
        return timeSpentMinutes;
    }

    public void setTimeSpentMinutes(Integer timeSpentMinutes) {
        this.timeSpentMinutes = timeSpentMinutes;
    }

    public LocalDateTime getLastAccessed() {
        return lastAccessed;
    }

    public void setLastAccessed(LocalDateTime lastAccessed) {
        this.lastAccessed = lastAccessed;
    }

    public Boolean getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(Boolean isCompleted) {
        this.isCompleted = isCompleted;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public String getBookmarkPosition() {
        return bookmarkPosition;
    }

    public void setBookmarkPosition(String bookmarkPosition) {
        this.bookmarkPosition = bookmarkPosition;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    // 新增字段的getter和setter
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getPauseCount() {
        return pauseCount;
    }

    public void setPauseCount(Integer pauseCount) {
        this.pauseCount = pauseCount;
    }

    public Integer getTotalPauseTimeMinutes() {
        return totalPauseTimeMinutes;
    }

    public void setTotalPauseTimeMinutes(Integer totalPauseTimeMinutes) {
        this.totalPauseTimeMinutes = totalPauseTimeMinutes;
    }

    public Integer getInteractionCount() {
        return interactionCount;
    }

    public void setInteractionCount(Integer interactionCount) {
        this.interactionCount = interactionCount;
    }

    public Integer getQuizAttempts() {
        return quizAttempts;
    }

    public void setQuizAttempts(Integer quizAttempts) {
        this.quizAttempts = quizAttempts;
    }

    public Integer getQuizScore() {
        return quizScore;
    }

    public void setQuizScore(Integer quizScore) {
        this.quizScore = quizScore;
    }

    public Integer getDifficultyRating() {
        return difficultyRating;
    }

    public void setDifficultyRating(Integer difficultyRating) {
        this.difficultyRating = difficultyRating;
    }

    public Integer getSatisfactionRating() {
        return satisfactionRating;
    }

    public void setSatisfactionRating(Integer satisfactionRating) {
        this.satisfactionRating = satisfactionRating;
    }

    public String getLearningPath() {
        return learningPath;
    }

    public void setLearningPath(String learningPath) {
        this.learningPath = learningPath;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getBrowserInfo() {
        return browserInfo;
    }

    public void setBrowserInfo(String browserInfo) {
        this.browserInfo = browserInfo;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public Boolean getIsMobileDevice() {
        return isMobileDevice;
    }

    public void setIsMobileDevice(Boolean isMobileDevice) {
        this.isMobileDevice = isMobileDevice;
    }

    public Integer getVideoWatchTimeSeconds() {
        return videoWatchTimeSeconds;
    }

    public void setVideoWatchTimeSeconds(Integer videoWatchTimeSeconds) {
        this.videoWatchTimeSeconds = videoWatchTimeSeconds;
    }

    public Integer getDocumentReadTimeSeconds() {
        return documentReadTimeSeconds;
    }

    public void setDocumentReadTimeSeconds(Integer documentReadTimeSeconds) {
        this.documentReadTimeSeconds = documentReadTimeSeconds;
    }

    public LocalDateTime getLastBookmarkTime() {
        return lastBookmarkTime;
    }

    public void setLastBookmarkTime(LocalDateTime lastBookmarkTime) {
        this.lastBookmarkTime = lastBookmarkTime;
    }

    public Boolean getAutoSaved() {
        return autoSaved;
    }

    public void setAutoSaved(Boolean autoSaved) {
        this.autoSaved = autoSaved;
    }

    // Helper methods
    public void updateProgress(Integer percentage) {
        this.progressPercentage = percentage;
        this.lastAccessed = LocalDateTime.now();
        
        if (percentage >= 100 && !isCompleted) {
            this.isCompleted = true;
            this.completionDate = LocalDateTime.now();
        }
    }

    public void addTimeSpent(Integer minutes) {
        this.timeSpentMinutes += minutes;
        this.lastAccessed = LocalDateTime.now();
    }

    public void markCompleted() {
        this.isCompleted = true;
        this.progressPercentage = 100;
        this.completionDate = LocalDateTime.now();
        this.lastAccessed = LocalDateTime.now();
    }
}
