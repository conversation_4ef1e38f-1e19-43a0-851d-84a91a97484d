package com.pharma.dms.controller;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.TrainingCourse;
import com.pharma.dms.entity.User;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.TrainingCourseService;
import com.pharma.dms.service.UserService;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/training-courses")
@CrossOrigin(origins = "*", maxAge = 3600)
public class TrainingCourseController {

    @Autowired
    private TrainingCourseService courseService;

    @Autowired
    private UserService userService;

    @Autowired
    private com.pharma.dms.repository.TrainingCourseRepository courseRepository;

    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAllCourses(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            System.out.println("=== 获取培训课程列表开始 ===");
            System.out.println("分页参数: page=" + page + ", size=" + size + ", sortBy=" + sortBy + ", sortDir=" + sortDir);

            // 暂时不使用排序，简化实现

            System.out.println("直接从repository获取课程...");
            // 完全绕过service，直接使用repository
            List<TrainingCourse> allCourses;
            try {
                allCourses = courseRepository.findAll();
                System.out.println("获取到课程数量: " + allCourses.size());
            } catch (Exception e) {
                System.out.println("获取课程列表异常: " + e.getMessage());
                e.printStackTrace();
                // 返回空列表而不是抛出异常
                allCourses = List.of();
                System.out.println("使用空课程列表");
            }

            // 手动分页
            int startIndex = page * size;
            int endIndex = Math.min(startIndex + size, allCourses.size());
            List<TrainingCourse> coursesForPage = allCourses.subList(startIndex, endIndex);

            // 创建简化的响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("content", coursesForPage.stream().map(course -> {
                Map<String, Object> courseData = new HashMap<>();
                courseData.put("id", course.getId());
                courseData.put("title", course.getTitle());
                courseData.put("description", course.getDescription());
                courseData.put("courseType", course.getCourseType().toString());
                courseData.put("status", course.getStatus().toString());
                courseData.put("durationMinutes", course.getDurationMinutes());
                courseData.put("isMandatory", course.getIsMandatory());
                courseData.put("createdAt", course.getCreatedAt().toString());
                if (course.getInstructor() != null) {
                    courseData.put("instructor", Map.of(
                        "id", course.getInstructor().getId(),
                        "username", course.getInstructor().getUsername(),
                        "firstName", course.getInstructor().getFirstName(),
                        "lastName", course.getInstructor().getLastName()
                    ));
                }
                return courseData;
            }).toList());

            // 手动计算分页信息
            int totalPages = (int) Math.ceil((double) allCourses.size() / size);
            responseData.put("pageable", Map.of(
                "pageNumber", page,
                "pageSize", size,
                "offset", startIndex,
                "paged", true,
                "unpaged", false
            ));

            responseData.put("totalElements", (long) allCourses.size());
            responseData.put("totalPages", totalPages);
            responseData.put("last", page >= totalPages - 1);
            responseData.put("first", page == 0);
            responseData.put("numberOfElements", coursesForPage.size());
            responseData.put("size", size);
            responseData.put("number", page);
            responseData.put("empty", coursesForPage.isEmpty());

            System.out.println("培训课程列表获取成功");
            return ResponseEntity.ok(ApiResponse.success("Courses retrieved successfully", responseData));
        } catch (Exception e) {
            System.out.println("获取培训课程列表失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to retrieve courses", e.getMessage()));
        }
    }

    @GetMapping("/active")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<TrainingCourse>>> getActiveCourses() {
        List<TrainingCourse> courses = courseService.getActiveCourses();
        return ResponseEntity.ok(ApiResponse.success("Active courses retrieved", courses));
    }

    @GetMapping("/mandatory")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<TrainingCourse>>> getMandatoryCourses() {
        List<TrainingCourse> courses = courseService.getMandatoryCourses();
        return ResponseEntity.ok(ApiResponse.success("Mandatory courses retrieved", courses));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<TrainingCourse>> getCourseById(@PathVariable Long id) {
        Optional<TrainingCourse> course = courseService.getCourseById(id);
        
        if (course.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success("Course found", course.get()));
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/code/{courseCode}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<TrainingCourse>> getCourseByCourseCode(@PathVariable String courseCode) {
        Optional<TrainingCourse> course = courseService.getCourseByCourseCode(courseCode);
        
        if (course.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success("Course found", course.get()));
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> createCourse(
            @RequestBody Map<String, Object> courseData,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 创建培训课程开始 ===");
            System.out.println("用户: " + userPrincipal.getUsername());
            System.out.println("课程数据: " + courseData);

            // 验证必要字段
            String courseCode = (String) courseData.get("courseCode");
            String title = (String) courseData.get("title");

            if (courseCode == null || courseCode.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("课程代码不能为空", null));
            }

            if (title == null || title.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("课程标题不能为空", null));
            }

            // 检查课程代码是否已存在
            if (courseService.getCourseByCourseCode(courseCode.trim()).isPresent()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("课程代码已存在: " + courseCode, null));
            }

            // 获取创建者
            User creator = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            // 创建课程对象并设置基本信息
            TrainingCourse course = new TrainingCourse();
            course.setCourseCode(courseCode.trim());
            course.setTitle(title.trim());

            // 设置可选字段
            if (courseData.containsKey("description")) {
                course.setDescription((String) courseData.get("description"));
            }

            // 设置课程类型
            if (courseData.containsKey("courseType")) {
                try {
                    String typeStr = (String) courseData.get("courseType");
                    course.setCourseType(TrainingCourse.CourseType.valueOf(typeStr.toUpperCase()));
                } catch (Exception e) {
                    course.setCourseType(TrainingCourse.CourseType.GENERAL);
                }
            }

            // 设置内容类型
            if (courseData.containsKey("contentType")) {
                try {
                    String contentTypeStr = (String) courseData.get("contentType");
                    course.setContentType(TrainingCourse.ContentType.valueOf(contentTypeStr.toUpperCase()));
                } catch (Exception e) {
                    course.setContentType(TrainingCourse.ContentType.MIXED);
                }
            }

            // 设置数值字段
            if (courseData.containsKey("durationMinutes")) {
                course.setDurationMinutes(((Number) courseData.get("durationMinutes")).intValue());
            }

            if (courseData.containsKey("passingScore")) {
                course.setPassingScore(((Number) courseData.get("passingScore")).intValue());
            }

            if (courseData.containsKey("maxAttempts")) {
                course.setMaxAttempts(((Number) courseData.get("maxAttempts")).intValue());
            }

            if (courseData.containsKey("estimatedHours")) {
                course.setEstimatedHours(((Number) courseData.get("estimatedHours")).doubleValue());
            }

            // 设置布尔字段
            if (courseData.containsKey("isMandatory")) {
                course.setIsMandatory((Boolean) courseData.get("isMandatory"));
            }

            if (courseData.containsKey("autoAssign")) {
                course.setAutoAssign((Boolean) courseData.get("autoAssign"));
            }

            // 设置内容相关字段
            if (courseData.containsKey("videoUrl")) {
                course.setVideoUrl((String) courseData.get("videoUrl"));
            }

            if (courseData.containsKey("documentPath")) {
                course.setDocumentPath((String) courseData.get("documentPath"));
            }

            if (courseData.containsKey("externalLink")) {
                course.setExternalLink((String) courseData.get("externalLink"));
            }

            System.out.println("准备保存课程: " + course.getTitle());

            // 使用增强的Service方法创建课程
            TrainingCourse savedCourse = courseService.createCourse(course, creator);

            // 创建详细的响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", savedCourse.getId());
            responseData.put("title", savedCourse.getTitle());
            responseData.put("courseCode", savedCourse.getCourseCode());
            responseData.put("description", savedCourse.getDescription());
            responseData.put("status", savedCourse.getStatus().toString());
            responseData.put("courseType", savedCourse.getCourseType().toString());
            responseData.put("contentType", savedCourse.getContentType().toString());
            responseData.put("difficultyLevel", savedCourse.getDifficultyLevel().toString());
            responseData.put("durationMinutes", savedCourse.getDurationMinutes());
            responseData.put("passingScore", savedCourse.getPassingScore());
            responseData.put("maxAttempts", savedCourse.getMaxAttempts());
            responseData.put("estimatedHours", savedCourse.getEstimatedHours());
            responseData.put("isMandatory", savedCourse.getIsMandatory());
            responseData.put("autoAssign", savedCourse.getAutoAssign());
            responseData.put("language", savedCourse.getLanguage());
            responseData.put("createdAt", savedCourse.getCreatedAt());
            responseData.put("instructor", Map.of(
                "id", savedCourse.getInstructor().getId(),
                "username", savedCourse.getInstructor().getUsername(),
                "firstName", savedCourse.getInstructor().getFirstName(),
                "lastName", savedCourse.getInstructor().getLastName()
            ));

            System.out.println("✅ 培训课程创建成功: " + savedCourse.getId());
            return ResponseEntity.ok(ApiResponse.success("Course created successfully", responseData));

        } catch (Exception e) {
            System.err.println("❌ 创建培训课程失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to create course", e.getMessage()));
        }
    }

    @PostMapping("/upload")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> createCourseWithFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("title") String title,
            @RequestParam("courseCode") String courseCode,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "courseType", defaultValue = "GENERAL") String courseType,
            @RequestParam(value = "contentType", defaultValue = "DOCUMENT") String contentType,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 创建带文件的培训课程 ===");
            System.out.println("文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + file.getSize());

            // 验证文件
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文件不能为空", null));
            }

            // 验证文件类型
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.toLowerCase().endsWith(".pdf") &&
                                   !fileName.toLowerCase().endsWith(".doc") &&
                                   !fileName.toLowerCase().endsWith(".docx") &&
                                   !fileName.toLowerCase().endsWith(".ppt") &&
                                   !fileName.toLowerCase().endsWith(".pptx"))) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("只支持PDF、Word、PowerPoint文件", null));
            }

            // 获取创建者
            User creator = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            // 保存文件
            String uploadDir = "uploads/training-courses";
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            String uniqueFileName = System.currentTimeMillis() + "_" + fileName;
            Path filePath = uploadPath.resolve(uniqueFileName);
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

            // 创建课程对象
            TrainingCourse course = new TrainingCourse();
            course.setCourseCode(courseCode.trim());
            course.setTitle(title.trim());
            course.setDescription(description != null ? description.trim() : "");
            course.setCourseType(TrainingCourse.CourseType.valueOf(courseType.toUpperCase()));
            course.setContentType(TrainingCourse.ContentType.valueOf(contentType.toUpperCase()));
            course.setDocumentPath(filePath.toString());

            // 使用Service创建课程
            TrainingCourse savedCourse = courseService.createCourse(course, creator);

            // 创建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", savedCourse.getId());
            responseData.put("title", savedCourse.getTitle());
            responseData.put("courseCode", savedCourse.getCourseCode());
            responseData.put("fileName", fileName);
            responseData.put("filePath", savedCourse.getDocumentPath());

            System.out.println("✅ 带文件的培训课程创建成功");
            return ResponseEntity.ok(ApiResponse.success("Course with file created successfully", responseData));

        } catch (Exception e) {
            System.err.println("❌ 创建带文件的培训课程失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to create course with file", e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<TrainingCourse>> updateCourse(
            @PathVariable Long id,
            @Valid @RequestBody TrainingCourse courseDetails) {
        try {
            TrainingCourse updatedCourse = courseService.updateCourse(id, courseDetails);
            return ResponseEntity.ok(ApiResponse.success("Course updated successfully", updatedCourse));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update course", e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteCourse(@PathVariable Long id) {
        try {
            courseService.deleteCourse(id);
            return ResponseEntity.ok(ApiResponse.success("Course deleted successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete course", e.getMessage()));
        }
    }

    @PostMapping("/{id}/submit-approval")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> submitCourseForApproval(
            @PathVariable Long id,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 提交课程审批 ===");
            System.out.println("课程ID: " + id);
            System.out.println("提交人: " + userPrincipal.getUsername());

            TrainingCourse course = courseService.getCourseById(id)
                    .orElseThrow(() -> new RuntimeException("Course not found"));

            // 检查课程状态
            if (course.getStatus() != TrainingCourse.CourseStatus.DRAFT) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("只有草稿状态的课程才能提交审批", null));
            }

            // 更新课程状态为审核中
            course.setStatus(TrainingCourse.CourseStatus.UNDER_REVIEW);
            TrainingCourse updatedCourse = courseService.updateCourse(id, course);

            Map<String, Object> result = new HashMap<>();
            result.put("courseId", id);
            result.put("status", updatedCourse.getStatus().toString());

            System.out.println("✅ 课程提交审批成功");
            return ResponseEntity.ok(ApiResponse.success("课程已提交审批", result));
        } catch (RuntimeException e) {
            System.err.println("❌ 提交课程审批失败: " + e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("提交审批失败", e.getMessage()));
        }
    }

    @PostMapping("/{id}/approve")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> approveCourse(
            @PathVariable Long id,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 批准课程 ===");
            System.out.println("课程ID: " + id);
            System.out.println("审批人: " + userPrincipal.getUsername());

            User approver = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            TrainingCourse course = courseService.getCourseById(id)
                    .orElseThrow(() -> new RuntimeException("Course not found"));

            // 检查课程状态
            if (course.getStatus() != TrainingCourse.CourseStatus.UNDER_REVIEW) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("只有审核中的课程才能批准", null));
            }

            // 更新课程状态为已批准
            course.setStatus(TrainingCourse.CourseStatus.APPROVED);
            TrainingCourse updatedCourse = courseService.updateCourse(id, course);

            Map<String, Object> result = new HashMap<>();
            result.put("courseId", id);
            result.put("status", updatedCourse.getStatus().toString());
            result.put("approvedBy", userPrincipal.getUsername());

            System.out.println("✅ 课程批准成功");
            return ResponseEntity.ok(ApiResponse.success("课程审批成功", result));
        } catch (RuntimeException e) {
            System.err.println("❌ 批准课程失败: " + e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批准失败", e.getMessage()));
        }
    }

    @PostMapping("/{id}/reject")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> rejectCourse(
            @PathVariable Long id,
            @RequestBody Map<String, String> request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 拒绝课程 ===");
            System.out.println("课程ID: " + id);
            System.out.println("审批人: " + userPrincipal.getUsername());

            String rejectionReason = request.get("rejectionReason");
            if (rejectionReason == null || rejectionReason.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("拒绝原因不能为空", null));
            }

            TrainingCourse course = courseService.getCourseById(id)
                    .orElseThrow(() -> new RuntimeException("Course not found"));

            // 检查课程状态
            if (course.getStatus() != TrainingCourse.CourseStatus.UNDER_REVIEW) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("只有审核中的课程才能拒绝", null));
            }

            // 更新课程状态回到草稿
            course.setStatus(TrainingCourse.CourseStatus.DRAFT);
            TrainingCourse updatedCourse = courseService.updateCourse(id, course);

            Map<String, Object> result = new HashMap<>();
            result.put("courseId", id);
            result.put("status", updatedCourse.getStatus().toString());
            result.put("rejectionReason", rejectionReason);

            System.out.println("✅ 课程拒绝成功");
            return ResponseEntity.ok(ApiResponse.success("课程已拒绝", result));
        } catch (RuntimeException e) {
            System.err.println("❌ 拒绝课程失败: " + e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("拒绝失败", e.getMessage()));
        }
    }

    @PostMapping("/{id}/activate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<TrainingCourse>> activateCourse(@PathVariable Long id) {
        try {
            TrainingCourse activatedCourse = courseService.activateCourse(id);
            return ResponseEntity.ok(ApiResponse.success("Course activated successfully", activatedCourse));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to activate course", e.getMessage()));
        }
    }

    @PostMapping("/{courseId}/assign/user/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Void>> assignCourseToUser(
            @PathVariable Long courseId,
            @PathVariable Long userId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User assignedBy = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            courseService.assignCourseToUser(courseId, userId, assignedBy);
            return ResponseEntity.ok(ApiResponse.success("Course assigned to user successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to assign course", e.getMessage()));
        }
    }

    @PostMapping("/{courseId}/assign/department/{departmentId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Void>> assignCourseToDepartment(
            @PathVariable Long courseId,
            @PathVariable Long departmentId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User assignedBy = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            courseService.assignCourseToDepartment(courseId, departmentId, assignedBy);
            return ResponseEntity.ok(ApiResponse.success("Course assigned to department successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to assign course", e.getMessage()));
        }
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Page<TrainingCourse>>> searchCourses(
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String courseCode,
            @RequestParam(required = false) TrainingCourse.CourseType courseType,
            @RequestParam(required = false) TrainingCourse.CourseStatus status,
            @RequestParam(required = false) Long instructorId,
            @RequestParam(required = false) Boolean isMandatory,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : 
                   Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<TrainingCourse> courses = courseService.searchCourses(title, courseCode, courseType,
                status, instructorId, isMandatory, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("Search completed", courses));
    }

    @GetMapping("/available")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<TrainingCourse>>> getAvailableCoursesForUser(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User user = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            List<TrainingCourse> courses = courseService.getAvailableCoursesForUser(user);
            return ResponseEntity.ok(ApiResponse.success("Available courses retrieved", courses));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get available courses", e.getMessage()));
        }
    }

    @GetMapping("/stats")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCourseStats() {
        try {
            Map<String, Object> stats = Map.of(
                "totalCourses", courseService.getTotalCourseCount(),
                "activeCourses", courseService.getCourseCountByStatus(TrainingCourse.CourseStatus.ACTIVE),
                "draftCourses", courseService.getCourseCountByStatus(TrainingCourse.CourseStatus.DRAFT),
                "mandatoryCourses", courseService.getMandatoryCourseCount(),
                "awaitingApproval", courseService.getCoursesAwaitingApproval().size()
            );

            return ResponseEntity.ok(ApiResponse.success("Course statistics", stats));
        } catch (Exception e) {
            System.err.println("Error getting course stats: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get course statistics", e.getMessage()));
        }
    }

    @GetMapping("/recent")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<TrainingCourse>>> getRecentCourses(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<TrainingCourse> recentCourses = courseService.getRecentCourses(limit);
        return ResponseEntity.ok(ApiResponse.success("Recent courses retrieved", recentCourses));
    }

    @GetMapping("/popular")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<TrainingCourse>>> getPopularCourses(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<TrainingCourse> popularCourses = courseService.getPopularCourses(limit);
        return ResponseEntity.ok(ApiResponse.success("Popular courses retrieved", popularCourses));
    }

    @GetMapping("/expiring")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<List<TrainingCourse>>> getExpiringCourses(
            @RequestParam(defaultValue = "30") int days) {
        
        List<TrainingCourse> expiringCourses = courseService.getExpiringCourses(days);
        return ResponseEntity.ok(ApiResponse.success("Expiring courses retrieved", expiringCourses));
    }

    @GetMapping("/awaiting-approval")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<List<TrainingCourse>>> getCoursesAwaitingApproval() {
        List<TrainingCourse> courses = courseService.getCoursesAwaitingApproval();
        return ResponseEntity.ok(ApiResponse.success("Courses awaiting approval retrieved", courses));
    }
}
