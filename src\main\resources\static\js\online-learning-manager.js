/**
 * 在线学习管理器
 * 负责在线学习的前端交互、进度跟踪、断点续学等功能
 */
class OnlineLearningManager {
    constructor() {
        this.sessionId = null;
        this.courseId = null;
        this.currentProgress = 0;
        this.timeSpent = 0;
        this.startTime = null;
        this.lastSaveTime = null;
        this.heartbeatInterval = null;
        this.autoSaveInterval = null;
        this.interactionCount = 0;
        this.isActive = true;
        this.bookmarkPosition = null;
        
        // 配置参数
        this.config = {
            heartbeatInterval: 30000, // 30秒心跳
            autoSaveInterval: 60000,  // 1分钟自动保存
            progressSaveThreshold: 5, // 进度变化5%才保存
            inactivityTimeout: 300000 // 5分钟无活动超时
        };

        this.init();
    }

    init() {
        console.log('=== 在线学习管理器初始化 ===');
        this.bindEvents();
        this.detectDeviceInfo();
        this.startActivityMonitoring();
    }

    // ========== 学习会话管理 ==========

    async startLearningSession(courseId) {
        try {
            console.log('=== 开始学习会话 ===');
            console.log('课程ID:', courseId);

            this.courseId = courseId;
            this.startTime = new Date();

            const deviceInfo = this.getDeviceInfo();
            
            const response = await authUtils.secureApiCall('/dms/api/learning/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    courseId: courseId,
                    deviceInfo: deviceInfo
                })
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 学习会话开始成功:', result);

                this.sessionId = result.data.sessionId;
                this.currentProgress = result.data.currentProgress || 0;
                this.timeSpent = result.data.timeSpent || 0;
                this.bookmarkPosition = result.data.bookmarkPosition;

                // 更新UI
                this.updateProgressDisplay();
                this.updateTimeDisplay();

                // 开始心跳和自动保存
                this.startHeartbeat();
                this.startAutoSave();

                // 如果有书签位置，询问是否继续
                if (this.bookmarkPosition && this.currentProgress > 0) {
                    this.showResumeDialog();
                }

                return result.data;
            } else {
                throw new Error('学习会话开始失败');
            }
        } catch (error) {
            console.error('❌ 开始学习会话失败:', error);
            this.showNotification('学习会话开始失败: ' + error.message, 'error');
            throw error;
        }
    }

    async updateProgress(progressPercentage, position = null) {
        if (!this.sessionId) return;

        const oldProgress = this.currentProgress;
        this.currentProgress = progressPercentage;
        
        // 计算时长
        if (this.startTime) {
            this.timeSpent = Math.floor((new Date() - this.startTime) / 60000); // 分钟
        }

        // 更新UI
        this.updateProgressDisplay();
        this.updateTimeDisplay();

        // 如果进度变化超过阈值，立即保存
        if (Math.abs(progressPercentage - oldProgress) >= this.config.progressSaveThreshold) {
            await this.saveProgress(position);
        }

        // 检查是否完成
        if (progressPercentage >= 100) {
            await this.completeLearning();
        }
    }

    async saveProgress(position = null) {
        if (!this.sessionId) return;

        try {
            if (position) {
                this.bookmarkPosition = position;
            }

            const response = await authUtils.secureApiCall('/dms/api/learning/progress', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionId: this.sessionId,
                    progressPercentage: this.currentProgress,
                    timeSpentMinutes: this.timeSpent,
                    bookmarkPosition: this.bookmarkPosition
                })
            });

            if (response.ok) {
                this.lastSaveTime = new Date();
                console.log('✅ 进度保存成功');
                this.showSaveIndicator();
            }
        } catch (error) {
            console.error('❌ 进度保存失败:', error);
        }
    }

    async saveBookmark(position, notes = null) {
        if (!this.sessionId) return;

        try {
            const response = await authUtils.secureApiCall('/dms/api/learning/bookmark', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionId: this.sessionId,
                    position: position,
                    notes: notes
                })
            });

            if (response.ok) {
                this.bookmarkPosition = position;
                this.showNotification('书签保存成功', 'success');
            }
        } catch (error) {
            console.error('❌ 书签保存失败:', error);
            this.showNotification('书签保存失败', 'error');
        }
    }

    async completeLearning() {
        if (!this.sessionId) return;

        try {
            console.log('=== 完成学习 ===');

            const response = await authUtils.secureApiCall('/dms/api/learning/complete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionId: this.sessionId,
                    finalProgress: 100,
                    totalTimeSpent: this.timeSpent,
                    finalPosition: this.bookmarkPosition
                })
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 学习完成:', result);

                this.stopHeartbeat();
                this.stopAutoSave();

                this.showCompletionDialog(result.data);
            }
        } catch (error) {
            console.error('❌ 完成学习失败:', error);
        }
    }

    async endSession() {
        if (!this.sessionId) return;

        try {
            console.log('=== 结束学习会话 ===');

            // 最后一次保存进度
            await this.saveProgress();

            const response = await authUtils.secureApiCall('/dms/api/learning/end', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionId: this.sessionId,
                    finalProgress: this.currentProgress,
                    totalTimeSpent: this.timeSpent,
                    finalPosition: this.bookmarkPosition
                })
            });

            if (response.ok) {
                console.log('✅ 学习会话结束成功');
            }
        } catch (error) {
            console.error('❌ 结束学习会话失败:', error);
        } finally {
            this.cleanup();
        }
    }

    // ========== 心跳和监控 ==========

    startHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }

        this.heartbeatInterval = setInterval(async () => {
            if (this.sessionId && this.isActive) {
                try {
                    await authUtils.secureApiCall('/dms/api/learning/heartbeat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            sessionId: this.sessionId
                        })
                    });
                } catch (error) {
                    console.error('❌ 心跳失败:', error);
                }
            }
        }, this.config.heartbeatInterval);
    }

    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    startAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }

        this.autoSaveInterval = setInterval(() => {
            if (this.sessionId) {
                this.saveProgress();
            }
        }, this.config.autoSaveInterval);
    }

    stopAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
    }

    startActivityMonitoring() {
        // 监听用户活动
        ['click', 'scroll', 'keypress', 'mousemove'].forEach(event => {
            document.addEventListener(event, () => {
                this.recordInteraction();
                this.resetInactivityTimer();
            });
        });

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.isActive = !document.hidden;
            if (this.isActive) {
                console.log('页面变为活跃状态');
            } else {
                console.log('页面变为非活跃状态');
            }
        });

        // 监听页面卸载
        window.addEventListener('beforeunload', (e) => {
            this.endSession();
        });
    }

    recordInteraction() {
        this.interactionCount++;
        
        // 每10次交互记录一次
        if (this.interactionCount % 10 === 0 && this.sessionId) {
            authUtils.secureApiCall('/dms/api/learning/interaction', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionId: this.sessionId,
                    interactionType: 'user_activity'
                })
            }).catch(error => {
                console.error('❌ 交互记录失败:', error);
            });
        }
    }

    resetInactivityTimer() {
        if (this.inactivityTimer) {
            clearTimeout(this.inactivityTimer);
        }

        this.inactivityTimer = setTimeout(() => {
            console.log('用户长时间无活动，暂停学习');
            this.pauseLearning();
        }, this.config.inactivityTimeout);
    }

    pauseLearning() {
        this.isActive = false;
        this.saveProgress();
        this.showInactivityDialog();
    }

    resumeLearning() {
        this.isActive = true;
        this.resetInactivityTimer();
        this.hideInactivityDialog();
    }

    // ========== UI更新方法 ==========

    updateProgressDisplay() {
        const progressBar = document.getElementById('trainingProgress');
        const progressText = document.getElementById('progressText');
        
        if (progressBar) {
            progressBar.style.width = this.currentProgress + '%';
            progressBar.textContent = this.currentProgress + '%';
        }
        
        if (progressText) {
            progressText.textContent = this.currentProgress + '%';
        }
    }

    updateTimeDisplay() {
        const timeDisplay = document.getElementById('timeSpent');
        if (timeDisplay) {
            const hours = Math.floor(this.timeSpent / 60);
            const minutes = this.timeSpent % 60;
            timeDisplay.textContent = `${hours}小时${minutes}分钟`;
        }
    }

    showSaveIndicator() {
        const indicator = document.getElementById('saveIndicator');
        if (indicator) {
            indicator.style.display = 'block';
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 2000);
        }
    }

    showResumeDialog() {
        if (confirm(`检测到您之前的学习进度(${this.currentProgress}%)，是否继续上次的学习？`)) {
            // 跳转到书签位置
            if (this.bookmarkPosition) {
                this.jumpToPosition(this.bookmarkPosition);
            }
        }
    }

    showCompletionDialog(data) {
        const message = `恭喜！您已完成本次培训！\n学习时长: ${data.sessionDuration}分钟\n总进度: ${data.finalProgress}%`;
        alert(message);
        
        // 可以跳转到证书页面或培训列表
        setTimeout(() => {
            window.location.href = '/dms/my-training';
        }, 3000);
    }

    showInactivityDialog() {
        // 显示非活跃状态对话框
        const dialog = document.getElementById('inactivityDialog');
        if (dialog) {
            dialog.style.display = 'block';
        }
    }

    hideInactivityDialog() {
        const dialog = document.getElementById('inactivityDialog');
        if (dialog) {
            dialog.style.display = 'none';
        }
    }

    jumpToPosition(position) {
        // 根据内容类型跳转到指定位置
        console.log('跳转到位置:', position);
        // 具体实现取决于内容类型（视频、文档等）
    }

    // ========== 工具方法 ==========

    getDeviceInfo() {
        return {
            deviceType: this.isMobileDevice() ? 'mobile' : 'desktop',
            browserInfo: navigator.userAgent,
            screenResolution: `${screen.width}x${screen.height}`,
            isMobileDevice: this.isMobileDevice()
        };
    }

    detectDeviceInfo() {
        this.deviceInfo = this.getDeviceInfo();
        console.log('设备信息:', this.deviceInfo);
    }

    isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        // 可以集成更好的通知组件
        if (type === 'error') {
            alert('错误: ' + message);
        }
    }

    cleanup() {
        this.stopHeartbeat();
        this.stopAutoSave();
        
        if (this.inactivityTimer) {
            clearTimeout(this.inactivityTimer);
        }
        
        this.sessionId = null;
        this.isActive = false;
    }

    bindEvents() {
        // 绑定页面事件
        document.addEventListener('DOMContentLoaded', () => {
            // 绑定保存书签按钮
            const bookmarkBtn = document.getElementById('saveBookmarkBtn');
            if (bookmarkBtn) {
                bookmarkBtn.addEventListener('click', () => {
                    const position = this.getCurrentPosition();
                    const notes = prompt('请输入书签备注（可选）:');
                    this.saveBookmark(position, notes);
                });
            }

            // 绑定继续学习按钮
            const resumeBtn = document.getElementById('resumeLearningBtn');
            if (resumeBtn) {
                resumeBtn.addEventListener('click', () => {
                    this.resumeLearning();
                });
            }
        });
    }

    getCurrentPosition() {
        // 获取当前学习位置，具体实现取决于内容类型
        return window.pageYOffset || document.documentElement.scrollTop;
    }
}

// 全局实例
let onlineLearningManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    onlineLearningManager = new OnlineLearningManager();
    
    // 如果页面有courseId参数，自动开始学习会话
    const urlParams = new URLSearchParams(window.location.search);
    const courseId = urlParams.get('courseId');
    if (courseId) {
        onlineLearningManager.startLearningSession(parseInt(courseId));
    }
});
