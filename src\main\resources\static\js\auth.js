/**
 * 认证工具 - 极简版本，专注解决认证问题
 */

// 全局认证状态
window.authState = {
    token: null,
    user: null,
    initialized: false
};

// 初始化认证状态
function initAuth() {
    console.log('🔧 初始化认证状态...');

    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');

    if (token && userStr) {
        try {
            window.authState.token = token;
            window.authState.user = JSON.parse(userStr);
            window.authState.initialized = true;
            console.log('✅ 认证状态初始化成功:', window.authState.user.username);
            return true;
        } catch (e) {
            console.error('❌ 用户数据解析失败:', e);
            clearAuth();
            return false;
        }
    } else {
        console.log('❌ 没有找到认证信息');
        clearAuth();
        return false;
    }
}

// 清除认证状态
function clearAuth() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    window.authState = {
        token: null,
        user: null,
        initialized: false
    };
    console.log('🧹 认证状态已清除');
}

// 检查是否已登录
function isLoggedIn() {
    if (!window.authState.initialized) {
        initAuth();
    }
    return window.authState.token !== null;
}

// 获取当前用户
function getCurrentUser() {
    if (!window.authState.initialized) {
        initAuth();
    }
    return window.authState.user;
}

// 获取认证头
function getAuthHeaders() {
    if (!isLoggedIn()) {
        return {};
    }
    return {
        'Authorization': 'Bearer ' + window.authState.token,
        'Content-Type': 'application/json'
    };
}

// 获取文件上传认证头
function getAuthHeadersForUpload() {
    if (!isLoggedIn()) {
        return {};
    }
    return {
        'Authorization': 'Bearer ' + window.authState.token
    };
}

// 检查用户角色
function hasRole(role) {
    const user = getCurrentUser();
    return user && user.roles && user.roles.includes(role);
}

// 检查是否为管理员
function isAdmin() {
    return hasRole('ROLE_ADMIN');
}

// 检查是否为QA用户
function isQA() {
    return hasRole('ROLE_QA');
}

// 处理API响应错误
function handleApiError(response, defaultMessage = '操作失败') {
    if (response.status === 401) {
        // 未授权，清除token并跳转到登录页
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        alert('登录已过期，请重新登录');
        window.location.href = '/dms/login';
        return;
    }
    
    if (response.status === 403) {
        alert('权限不足，无法执行此操作');
        return;
    }
    
    // 其他错误
    response.json().then(data => {
        alert(data.message || defaultMessage);
    }).catch(() => {
        alert(defaultMessage);
    });
}

// 安全的API调用 - 详细调试版本
async function secureApiCall(url, options = {}) {
    console.log('🔗 API调用:', url);

    // 确保认证状态已初始化
    if (!isLoggedIn()) {
        console.error('❌ 未登录，无法调用API');

        // 返回一个模拟的未认证响应对象
        return {
            ok: false,
            status: 401,
            json: () => Promise.resolve({
                success: false,
                message: '用户未登录',
                error: '请先登录后再试'
            })
        };
    }

    // 详细检查token状态
    const token = window.authState.token;
    console.log('🔑 Token状态:');
    console.log('  - 存在:', token ? 'YES' : 'NO');
    console.log('  - 长度:', token ? token.length : 0);
    console.log('  - 前缀:', token ? token.substring(0, 50) + '...' : 'N/A');

    // 准备请求头
    const headers = options.headers || {};
    headers['Authorization'] = 'Bearer ' + token;

    if (!headers['Content-Type'] && options.method !== 'GET') {
        headers['Content-Type'] = 'application/json';
    }

    console.log('📤 请求头:');
    console.log('  - Authorization:', headers['Authorization'] ? headers['Authorization'].substring(0, 30) + '...' : 'Missing');
    console.log('  - Content-Type:', headers['Content-Type'] || 'Not set');

    const requestOptions = {
        ...options,
        headers
    };

    try {
        console.log('🚀 发送请求到:', url);
        const response = await fetch(url, requestOptions);
        console.log('📡 响应状态:', response.status);

        // 如果401，详细记录并重定向到登录页
        if (response.status === 401) {
            console.error('❌ 认证失败 (401)');
            console.error('  - URL:', url);
            console.error('  - Token长度:', token ? token.length : 0);
            console.error('  - 请求头:', headers);

            // 尝试获取响应内容
            try {
                const errorText = await response.text();
                console.error('  - 错误响应:', errorText);
            } catch (e) {
                console.error('  - 无法读取错误响应');
            }

            // 清除认证状态并重定向到登录页
            clearAuth();
            window.location.href = '/dms/login';
            return null;
        }

        // 检查其他错误状态码
        if (!response.ok) {
            console.error('❌ API调用失败:', response.status, response.statusText);
            console.error('  - URL:', url);

            // 克隆响应以便多次读取
            const responseClone = response.clone();

            // 尝试解析错误响应
            try {
                const errorData = await response.json();
                console.error('  - 错误详情:', errorData);
                return {
                    ok: false,
                    status: response.status,
                    json: () => Promise.resolve(errorData)
                };
            } catch (e) {
                // 如果无法解析JSON，返回文本错误
                try {
                    const errorText = await responseClone.text();
                    console.error('  - 错误文本:', errorText);

                    // 检查是否是HTML错误页面
                    if (errorText.includes('<html>') || errorText.includes('<!DOCTYPE')) {
                        console.error('  - 服务器返回了HTML错误页面而不是JSON');
                        return {
                            ok: false,
                            status: response.status,
                            json: () => Promise.resolve({
                                success: false,
                                message: '服务器内部错误',
                                error: `HTTP ${response.status}: 服务器返回了错误页面`
                            })
                        };
                    }

                    return {
                        ok: false,
                        status: response.status,
                        json: () => Promise.resolve({
                            success: false,
                            message: '服务器错误',
                            error: errorText || '未知错误'
                        })
                    };
                } catch (textError) {
                    console.error('  - 无法读取错误响应');
                    return {
                        ok: false,
                        status: response.status,
                        json: () => Promise.resolve({
                            success: false,
                            message: '服务器错误',
                            error: '无法读取响应内容'
                        })
                    };
                }
            }
        }

        return response;
    } catch (error) {
        console.error('❌ 网络错误:', error);

        // 返回一个模拟的错误响应对象
        return {
            ok: false,
            status: 0,
            json: () => Promise.resolve({
                success: false,
                message: '网络错误，请检查网络连接',
                error: error.message || '网络连接失败'
            })
        };
    }
}

// 安全的API响应处理函数
async function handleApiResponse(response, defaultErrorMessage = '操作失败') {
    if (!response) {
        throw new Error('网络连接失败，请检查网络状态');
    }

    if (response.ok) {
        try {
            const text = await response.text();
            if (!text || text.trim() === '') {
                throw new Error('服务器返回空响应');
            }
            return JSON.parse(text);
        } catch (jsonError) {
            console.error('JSON解析失败:', jsonError);
            throw new Error('服务器响应格式错误: ' + jsonError.message);
        }
    } else {
        try {
            const text = await response.text();
            if (text && text.trim() !== '') {
                const errorData = JSON.parse(text);
                throw new Error(errorData.message || errorData.error || defaultErrorMessage);
            } else {
                throw new Error(`${defaultErrorMessage} (HTTP ${response.status})`);
            }
        } catch (jsonError) {
            // 如果无法解析JSON，使用状态码信息
            throw new Error(`${defaultErrorMessage} (HTTP ${response.status})`);
        }
    }
}

// 安全的文件上传 - 详细调试版本
async function secureFileUpload(url, formData) {
    console.log('📁 文件上传:', url);

    // 确保认证状态已初始化
    if (!isLoggedIn()) {
        console.error('❌ 未登录，无法上传文件');
        return null;
    }

    // 详细检查token状态
    const token = window.authState.token;
    console.log('🔑 上传Token状态:');
    console.log('  - 存在:', token ? 'YES' : 'NO');
    console.log('  - 长度:', token ? token.length : 0);
    console.log('  - 前缀:', token ? token.substring(0, 50) + '...' : 'N/A');

    try {
        console.log('🚀 发送文件上传请求到:', url);
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + token
            },
            body: formData
        });

        console.log('📡 上传响应状态:', response.status);

        // 如果401，详细记录
        if (response.status === 401) {
            console.error('❌ 上传认证失败 (401)');
            console.error('  - URL:', url);
            console.error('  - Token长度:', token ? token.length : 0);

            // 尝试获取响应内容
            try {
                const errorText = await response.text();
                console.error('  - 错误响应:', errorText);
            } catch (e) {
                console.error('  - 无法读取错误响应');
            }

            return null;
        }

        return response;
    } catch (error) {
        console.error('❌ 上传网络错误:', error);
        return null;
    }
}

// 页面加载时检查认证状态 - 简化版本
function checkAuthOnPageLoad() {
    // 如果在登录页面，不需要检查
    if (window.location.pathname.includes('/login')) {
        return;
    }

    // 只记录状态，不做任何跳转
    const token = localStorage.getItem('token');
    console.log('Page loaded, token status:', token ? 'present' : 'missing');
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/dms/login';
    }
}

// 显示用户信息
function displayUserInfo() {
    const user = getCurrentUser();
    if (user) {
        // 更新页面上的用户信息显示
        const userNameElements = document.querySelectorAll('.user-name');
        userNameElements.forEach(element => {
            element.textContent = user.firstName + ' ' + user.lastName;
        });

        const userRoleElements = document.querySelectorAll('.user-role');
        userRoleElements.forEach(element => {
            element.textContent = user.roles.join(', ').replace(/ROLE_/g, '');
        });
    }
}

// 安全的Multipart API调用函数（专门用于AI生成等multipart请求）
async function secureMultipartApiCall(url, options = {}) {
    console.log('🔗 Multipart API调用:', url);

    if (!isLoggedIn()) {
        console.error('❌ 未登录，无法调用API');
        return null;
    }

    const token = window.authState.token;
    console.log('🔑 Token状态:');
    console.log('  - 存在:', token ? 'YES' : 'NO');
    console.log('  - 长度:', token ? token.length : 0);

    // 对于multipart请求，只设置Authorization头，让浏览器自动设置Content-Type
    const headers = {
        'Authorization': 'Bearer ' + token
    };

    console.log('📤 Multipart请求头:');
    console.log('  - Authorization:', headers['Authorization'] ? headers['Authorization'].substring(0, 30) + '...' : 'Missing');
    console.log('  - Content-Type: 自动设置 (multipart/form-data)');

    const requestOptions = {
        ...options,
        headers
    };

    try {
        console.log('🚀 发送Multipart请求到:', url);
        const response = await fetch(url, requestOptions);
        console.log('📡 响应状态:', response.status);

        if (response.status === 401) {
            console.error('❌ 认证失败 (401)');
            console.error('  - URL:', url);
            console.error('  - Token长度:', token ? token.length : 0);

            try {
                const errorText = await response.text();
                console.error('  - 错误响应:', errorText);
            } catch (e) {
                console.error('  - 无法读取错误响应');
            }

            // 清除认证状态并跳转登录
            clearAuth();
            window.location.href = '/dms/login';
            return null;
        }

        return response;
    } catch (error) {
        console.error('❌ Multipart API调用失败:', error);
        throw error;
    }
}

// 页面初始化 - 极简版本
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 认证工具已加载');

    // 初始化认证状态
    initAuth();

    // 显示用户信息
    displayUserInfo();

    // 如果在登录页面，不做任何检查
    if (window.location.pathname.includes('/login')) {
        console.log('📍 当前在登录页面，跳过认证检查');
        return;
    }

    // 检查认证状态
    if (isLoggedIn()) {
        console.log('✅ 用户已登录:', window.authState.user.username);
    } else {
        console.log('❌ 用户未登录');
        // 不自动跳转，让用户手动处理
    }
});

// 导出函数供全局使用
window.authUtils = {
    initAuth,
    clearAuth,
    isLoggedIn,
    getCurrentUser,
    getAuthHeaders,
    getAuthHeadersForUpload,
    secureApiCall,
    secureFileUpload,
    secureMultipartApiCall,
    handleApiResponse,
    logout,
    displayUserInfo
};
