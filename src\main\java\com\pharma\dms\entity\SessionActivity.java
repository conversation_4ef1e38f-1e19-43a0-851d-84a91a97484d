package com.pharma.dms.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 学习会话活动实体
 * 记录学习会话中的具体活动和交互
 */
@Entity
@Table(name = "session_activities")
public class SessionActivity extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "learning_session_id", nullable = false)
    private LearningSession learningSession;

    @Enumerated(EnumType.STRING)
    @Column(name = "activity_type", nullable = false)
    private ActivityType activityType;

    @Column(name = "activity_time", nullable = false)
    private LocalDateTime activityTime;

    @Column(name = "module_id")
    private Long moduleId;

    @Column(name = "content_id")
    private String contentId;

    @Column(name = "position_data")
    private String positionData; // JSON格式的位置数据

    @Column(name = "duration_seconds")
    private Integer durationSeconds;

    @Column(name = "interaction_data")
    private String interactionData; // JSON格式的交互数据

    @Column(name = "score")
    private Integer score;

    @Column(name = "result")
    private String result;

    @Column(name = "notes")
    private String notes;

    // Constructors
    public SessionActivity() {
        this.activityTime = LocalDateTime.now();
    }

    public SessionActivity(LearningSession learningSession, ActivityType activityType) {
        this();
        this.learningSession = learningSession;
        this.activityType = activityType;
    }

    // Getters and Setters
    public LearningSession getLearningSession() {
        return learningSession;
    }

    public void setLearningSession(LearningSession learningSession) {
        this.learningSession = learningSession;
    }

    public ActivityType getActivityType() {
        return activityType;
    }

    public void setActivityType(ActivityType activityType) {
        this.activityType = activityType;
    }

    public LocalDateTime getActivityTime() {
        return activityTime;
    }

    public void setActivityTime(LocalDateTime activityTime) {
        this.activityTime = activityTime;
    }

    public Long getModuleId() {
        return moduleId;
    }

    public void setModuleId(Long moduleId) {
        this.moduleId = moduleId;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public String getPositionData() {
        return positionData;
    }

    public void setPositionData(String positionData) {
        this.positionData = positionData;
    }

    public Integer getDurationSeconds() {
        return durationSeconds;
    }

    public void setDurationSeconds(Integer durationSeconds) {
        this.durationSeconds = durationSeconds;
    }

    public String getInteractionData() {
        return interactionData;
    }

    public void setInteractionData(String interactionData) {
        this.interactionData = interactionData;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    // Enums
    public enum ActivityType {
        SESSION_START("会话开始"),
        SESSION_END("会话结束"),
        SESSION_PAUSE("会话暂停"),
        SESSION_RESUME("会话恢复"),
        
        MODULE_START("模块开始"),
        MODULE_COMPLETE("模块完成"),
        MODULE_SKIP("模块跳过"),
        
        VIDEO_PLAY("视频播放"),
        VIDEO_PAUSE("视频暂停"),
        VIDEO_SEEK("视频跳转"),
        VIDEO_COMPLETE("视频完成"),
        
        DOCUMENT_OPEN("文档打开"),
        DOCUMENT_SCROLL("文档滚动"),
        DOCUMENT_BOOKMARK("文档书签"),
        DOCUMENT_COMPLETE("文档完成"),
        
        QUIZ_START("测验开始"),
        QUIZ_SUBMIT("测验提交"),
        QUIZ_COMPLETE("测验完成"),
        
        INTERACTION_CLICK("点击交互"),
        INTERACTION_HOVER("悬停交互"),
        INTERACTION_SCROLL("滚动交互"),
        
        BOOKMARK_ADD("添加书签"),
        BOOKMARK_REMOVE("移除书签"),
        
        NOTE_ADD("添加笔记"),
        NOTE_EDIT("编辑笔记"),
        NOTE_DELETE("删除笔记"),
        
        PROGRESS_SAVE("进度保存"),
        PROGRESS_LOAD("进度加载"),
        
        ERROR_OCCURRED("发生错误"),
        CONNECTION_LOST("连接丢失"),
        CONNECTION_RESTORED("连接恢复"),
        
        FEEDBACK_SUBMIT("提交反馈"),
        RATING_SUBMIT("提交评分"),
        
        HELP_REQUEST("请求帮助"),
        SUPPORT_CONTACT("联系支持"),
        
        CUSTOM("自定义活动");

        private final String displayName;

        ActivityType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // Helper methods
    public static SessionActivity createSessionStart(LearningSession session) {
        return new SessionActivity(session, ActivityType.SESSION_START);
    }

    public static SessionActivity createSessionEnd(LearningSession session) {
        return new SessionActivity(session, ActivityType.SESSION_END);
    }

    public static SessionActivity createVideoPlay(LearningSession session, String contentId, String position) {
        SessionActivity activity = new SessionActivity(session, ActivityType.VIDEO_PLAY);
        activity.setContentId(contentId);
        activity.setPositionData(position);
        return activity;
    }

    public static SessionActivity createQuizSubmit(LearningSession session, Long moduleId, Integer score, String result) {
        SessionActivity activity = new SessionActivity(session, ActivityType.QUIZ_SUBMIT);
        activity.setModuleId(moduleId);
        activity.setScore(score);
        activity.setResult(result);
        return activity;
    }

    public static SessionActivity createProgressSave(LearningSession session, String positionData) {
        SessionActivity activity = new SessionActivity(session, ActivityType.PROGRESS_SAVE);
        activity.setPositionData(positionData);
        return activity;
    }

    public static SessionActivity createInteraction(LearningSession session, ActivityType type, String data) {
        SessionActivity activity = new SessionActivity(session, type);
        activity.setInteractionData(data);
        return activity;
    }
}
