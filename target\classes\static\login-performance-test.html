<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录性能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .result { margin: 15px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .metric-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
        .test-history { max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; padding: 10px; }
        .test-item { padding: 8px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; }
        .test-item:last-child { border-bottom: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 登录性能测试工具</h1>
        
        <div class="form-group">
            <label for="username">用户名</label>
            <input type="text" id="username" value="admin" placeholder="输入用户名">
        </div>

        <div class="form-group">
            <label for="password">密码</label>
            <input type="password" id="password" value="admin123" placeholder="输入密码">
        </div>

        <div class="form-group">
            <label for="testCount">测试次数</label>
            <select id="testCount">
                <option value="1">1次</option>
                <option value="5" selected>5次</option>
                <option value="10">10次</option>
                <option value="20">20次</option>
            </select>
        </div>

        <div class="form-group">
            <button onclick="runSingleTest()" id="singleTestBtn">单次登录测试</button>
            <button onclick="runBatchTest()" id="batchTestBtn">批量性能测试</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div class="metrics" id="metricsContainer" style="display: none;">
            <div class="metric-card">
                <div class="metric-value" id="avgTime">-</div>
                <div class="metric-label">平均响应时间 (ms)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="minTime">-</div>
                <div class="metric-label">最快响应时间 (ms)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="maxTime">-</div>
                <div class="metric-label">最慢响应时间 (ms)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="successRate">-</div>
                <div class="metric-label">成功率 (%)</div>
            </div>
        </div>

        <div id="result" class="result" style="display: none;"></div>

        <div class="test-history" id="testHistory" style="display: none;">
            <h4>测试历史</h4>
            <div id="historyList"></div>
        </div>
    </div>

    <script>
        let testResults = [];
        let isRunning = false;

        // 单次登录测试
        async function runSingleTest() {
            if (isRunning) return;
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('请输入用户名和密码', 'warning');
                return;
            }

            try {
                isRunning = true;
                updateButtonStates(true);
                
                const result = await performLoginTest(username, password);
                testResults.push(result);
                
                showResult(`登录${result.success ? '成功' : '失败'}，响应时间: ${result.responseTime}ms`, 
                          result.success ? 'success' : 'error');
                
                updateMetrics();
                updateHistory();
                
            } catch (error) {
                showResult('测试异常: ' + error.message, 'error');
            } finally {
                isRunning = false;
                updateButtonStates(false);
            }
        }

        // 批量性能测试
        async function runBatchTest() {
            if (isRunning) return;
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const testCount = parseInt(document.getElementById('testCount').value);
            
            if (!username || !password) {
                showResult('请输入用户名和密码', 'warning');
                return;
            }

            try {
                isRunning = true;
                updateButtonStates(true);
                testResults = []; // 清空之前的结果
                
                showResult(`开始批量测试，共${testCount}次...`, 'info');
                
                for (let i = 0; i < testCount; i++) {
                    showResult(`正在执行第 ${i + 1}/${testCount} 次测试...`, 'info');
                    
                    const result = await performLoginTest(username, password);
                    testResults.push(result);
                    
                    // 测试间隔，避免过于频繁
                    if (i < testCount - 1) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }
                
                updateMetrics();
                updateHistory();
                
                const successCount = testResults.filter(r => r.success).length;
                showResult(`批量测试完成！成功: ${successCount}/${testCount}`, 'success');
                
            } catch (error) {
                showResult('批量测试异常: ' + error.message, 'error');
            } finally {
                isRunning = false;
                updateButtonStates(false);
            }
        }

        // 执行单次登录测试
        async function performLoginTest(username, password) {
            const startTime = performance.now();
            
            try {
                const response = await fetch('/dms/api/auth/signin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                const data = await response.json();
                
                return {
                    success: response.ok && data.token,
                    responseTime: responseTime,
                    status: response.status,
                    timestamp: new Date().toLocaleTimeString(),
                    error: response.ok ? null : (data.message || 'Unknown error')
                };
                
            } catch (error) {
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                return {
                    success: false,
                    responseTime: responseTime,
                    status: 0,
                    timestamp: new Date().toLocaleTimeString(),
                    error: error.message
                };
            }
        }

        // 更新性能指标
        function updateMetrics() {
            if (testResults.length === 0) return;
            
            const responseTimes = testResults.map(r => r.responseTime);
            const successCount = testResults.filter(r => r.success).length;
            
            const avgTime = Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length);
            const minTime = Math.min(...responseTimes);
            const maxTime = Math.max(...responseTimes);
            const successRate = Math.round((successCount / testResults.length) * 100);
            
            document.getElementById('avgTime').textContent = avgTime;
            document.getElementById('minTime').textContent = minTime;
            document.getElementById('maxTime').textContent = maxTime;
            document.getElementById('successRate').textContent = successRate;
            
            document.getElementById('metricsContainer').style.display = 'grid';
        }

        // 更新测试历史
        function updateHistory() {
            const historyList = document.getElementById('historyList');
            historyList.innerHTML = '';
            
            testResults.slice(-10).reverse().forEach((result, index) => {
                const item = document.createElement('div');
                item.className = 'test-item';
                item.innerHTML = `
                    <span>${result.timestamp}</span>
                    <span style="color: ${result.success ? 'green' : 'red'}">
                        ${result.success ? '✅' : '❌'} ${result.responseTime}ms
                    </span>
                `;
                historyList.appendChild(item);
            });
            
            document.getElementById('testHistory').style.display = 'block';
        }

        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // 更新按钮状态
        function updateButtonStates(running) {
            document.getElementById('singleTestBtn').disabled = running;
            document.getElementById('batchTestBtn').disabled = running;
            
            if (running) {
                document.getElementById('singleTestBtn').textContent = '测试中...';
                document.getElementById('batchTestBtn').textContent = '测试中...';
            } else {
                document.getElementById('singleTestBtn').textContent = '单次登录测试';
                document.getElementById('batchTestBtn').textContent = '批量性能测试';
            }
        }

        // 清除结果
        function clearResults() {
            testResults = [];
            document.getElementById('metricsContainer').style.display = 'none';
            document.getElementById('testHistory').style.display = 'none';
            document.getElementById('result').style.display = 'none';
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            console.log('登录性能测试工具已加载');
        });
    </script>
</body>
</html>
