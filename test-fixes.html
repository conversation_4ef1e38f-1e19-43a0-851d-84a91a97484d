<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS修复验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; white-space: pre-wrap; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
        .warning { background: #fff3e0; color: #f57c00; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; border: none; }
        .btn-success { background: #28a745; color: white; border: none; }
        .btn-warning { background: #ffc107; color: black; border: none; }
        input[type="file"] { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>DMS系统修复验证测试</h1>
    
    <div class="section">
        <h2>🔐 1. 登录测试</h2>
        <button class="btn-primary" onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="section">
        <h2>📊 2. 报表API测试</h2>
        <button class="btn-primary" onclick="testReportsAPI()">测试报表数据</button>
        <div id="reportsResult" class="result"></div>
    </div>

    <div class="section">
        <h2>⚙️ 3. 设置API测试</h2>
        <button class="btn-primary" onclick="testSettingsAPI()">测试设置配置</button>
        <div id="settingsResult" class="result"></div>
    </div>

    <div class="section">
        <h2>📄 4. 文档上传测试</h2>
        <input type="file" id="testFile" accept=".txt,.pdf,.doc,.docx">
        <br>
        <button class="btn-success" onclick="testDocumentUpload()">测试文档上传</button>
        <div id="uploadResult" class="result"></div>
    </div>

    <div class="section">
        <h2>📋 5. 文档列表测试</h2>
        <button class="btn-primary" onclick="testDocumentList()">测试文档列表</button>
        <div id="listResult" class="result"></div>
    </div>

    <div class="section">
        <h2>⬇️ 6. 文档下载测试</h2>
        <input type="number" id="downloadId" placeholder="文档ID" value="1" style="width: 100px;">
        <button class="btn-warning" onclick="testDocumentDownload()">测试文档下载</button>
        <div id="downloadResult" class="result"></div>
    </div>

    <script>
        let authToken = null;

        function log(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = 'result ' + type;
            console.log(message);
        }

        async function testLogin() {
            try {
                log('loginResult', '正在登录...', 'warning');
                
                const response = await fetch('/dms/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    authToken = result.data.token;
                    localStorage.setItem('token', authToken);
                    log('loginResult', `✅ 登录成功！\nToken: ${authToken.substring(0, 30)}...\n用户: ${result.data.username}`);
                } else {
                    const error = await response.text();
                    log('loginResult', `❌ 登录失败: ${error}`, 'error');
                }
            } catch (error) {
                log('loginResult', `❌ 登录异常: ${error.message}`, 'error');
            }
        }

        async function testReportsAPI() {
            if (!authToken) {
                log('reportsResult', '❌ 请先登录', 'error');
                return;
            }

            try {
                log('reportsResult', '正在测试报表API...', 'warning');
                
                const apis = [
                    '/dms/api/reports/user-activity',
                    '/dms/api/reports/document-stats',
                    '/dms/api/reports/training-stats'
                ];

                let results = '报表API测试结果:\n\n';

                for (const api of apis) {
                    try {
                        const response = await fetch(api, {
                            method: 'GET',
                            headers: {
                                'Authorization': 'Bearer ' + authToken,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            const result = await response.json();
                            results += `✅ ${api} - 成功\n`;
                            results += `   数据: ${JSON.stringify(result.data, null, 2).substring(0, 100)}...\n\n`;
                        } else {
                            results += `❌ ${api} - 失败 (${response.status})\n\n`;
                        }
                    } catch (error) {
                        results += `❌ ${api} - 异常: ${error.message}\n\n`;
                    }
                }

                log('reportsResult', results);
            } catch (error) {
                log('reportsResult', `❌ 报表API测试异常: ${error.message}`, 'error');
            }
        }

        async function testSettingsAPI() {
            if (!authToken) {
                log('settingsResult', '❌ 请先登录', 'error');
                return;
            }

            try {
                log('settingsResult', '正在测试设置API...', 'warning');
                
                const response = await fetch('/dms/api/settings/config', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + authToken,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    log('settingsResult', `✅ 设置API测试成功！\n状态: ${response.status}\n数据: ${JSON.stringify(result.data, null, 2)}`);
                } else {
                    const error = await response.text();
                    log('settingsResult', `❌ 设置API测试失败: ${error}`, 'error');
                }
            } catch (error) {
                log('settingsResult', `❌ 设置API测试异常: ${error.message}`, 'error');
            }
        }

        async function testDocumentUpload() {
            if (!authToken) {
                log('uploadResult', '❌ 请先登录', 'error');
                return;
            }

            const fileInput = document.getElementById('testFile');
            if (!fileInput.files[0]) {
                log('uploadResult', '❌ 请选择文件', 'error');
                return;
            }

            try {
                log('uploadResult', '正在上传文档...', 'warning');
                
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                formData.append('title', '测试文档 - ' + new Date().toLocaleString());
                formData.append('description', '这是一个测试上传的文档');

                const response = await fetch('/dms/api/documents/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    },
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    log('uploadResult', `✅ 文档上传成功！\n文档ID: ${result.data.id}\n标题: ${result.data.title}\n文件名: ${result.data.originalFileName}\n大小: ${result.data.fileSize} 字节`);
                } else {
                    const error = await response.text();
                    log('uploadResult', `❌ 文档上传失败: ${error}`, 'error');
                }
            } catch (error) {
                log('uploadResult', `❌ 文档上传异常: ${error.message}`, 'error');
            }
        }

        async function testDocumentList() {
            if (!authToken) {
                log('listResult', '❌ 请先登录', 'error');
                return;
            }

            try {
                log('listResult', '正在获取文档列表...', 'warning');
                
                const response = await fetch('/dms/api/documents?page=0&size=5', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const docs = result.data.content;
                    let message = `✅ 文档列表获取成功！\n总数: ${result.data.totalElements}\n当前页: ${docs.length} 个文档\n\n`;
                    docs.forEach((doc, index) => {
                        message += `${index + 1}. ID: ${doc.id}, 标题: ${doc.title}\n   文件: ${doc.originalFileName}, 大小: ${doc.fileSize} 字节\n\n`;
                    });
                    log('listResult', message);
                } else {
                    const error = await response.text();
                    log('listResult', `❌ 文档列表获取失败: ${error}`, 'error');
                }
            } catch (error) {
                log('listResult', `❌ 文档列表获取异常: ${error.message}`, 'error');
            }
        }

        async function testDocumentDownload() {
            if (!authToken) {
                log('downloadResult', '❌ 请先登录', 'error');
                return;
            }

            const docId = document.getElementById('downloadId').value;
            if (!docId) {
                log('downloadResult', '❌ 请输入文档ID', 'error');
                return;
            }

            try {
                log('downloadResult', '正在下载文档...', 'warning');
                
                const response = await fetch(`/dms/api/documents/${docId}/download`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + authToken
                    }
                });

                if (response.ok) {
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let fileName = 'download';
                    
                    if (contentDisposition) {
                        const fileNameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)/);
                        if (fileNameMatch) {
                            fileName = decodeURIComponent(fileNameMatch[1]);
                        }
                    }

                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    log('downloadResult', `✅ 文档下载成功！\n文件名: ${fileName}\n大小: ${blob.size} 字节`);
                } else {
                    const error = await response.text();
                    log('downloadResult', `❌ 文档下载失败: ${error}`, 'error');
                }
            } catch (error) {
                log('downloadResult', `❌ 文档下载异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时尝试从localStorage获取token
        window.onload = function() {
            const savedToken = localStorage.getItem('token');
            if (savedToken) {
                authToken = savedToken;
                log('loginResult', '✅ 已从本地存储加载Token');
            }
        };
    </script>
</body>
</html>
