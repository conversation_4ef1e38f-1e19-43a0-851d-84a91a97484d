package com.pharma.dms.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.User;
import com.pharma.dms.service.OnlineLearningService;
import com.pharma.dms.service.UserService;

/**
 * 在线学习控制器
 * 提供在线学习相关的API接口
 */
@RestController
@RequestMapping("/api/learning")
@CrossOrigin(origins = "*", maxAge = 3600)
public class OnlineLearningController {

    @Autowired
    private OnlineLearningService learningService;

    @Autowired
    private UserService userService;

    /**
     * 开始学习会话
     */
    @PostMapping("/start")
    @PreAuthorize("hasRole('USER') or hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> startLearningSession(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            String username = authentication.getName();
            User user = userService.getUserByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            Long courseId = Long.valueOf(request.get("courseId").toString());
            @SuppressWarnings("unchecked")
            Map<String, Object> deviceInfo = (Map<String, Object>) request.get("deviceInfo");
            
            Map<String, Object> sessionData = learningService.startLearningSession(courseId, user.getId(), deviceInfo);
            
            return ResponseEntity.ok(ApiResponse.success("学习会话开始成功", sessionData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("开始学习会话失败", e.getMessage()));
        }
    }

    /**
     * 更新学习进度
     */
    @PutMapping("/progress")
    @PreAuthorize("hasRole('USER') or hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> updateProgress(@RequestBody Map<String, Object> request) {
        try {
            String sessionId = (String) request.get("sessionId");
            Integer progressPercentage = (Integer) request.get("progressPercentage");
            Integer timeSpentMinutes = (Integer) request.get("timeSpentMinutes");
            String bookmarkPosition = (String) request.get("bookmarkPosition");
            
            learningService.updateLearningProgress(sessionId, progressPercentage, timeSpentMinutes, bookmarkPosition);
            
            return ResponseEntity.ok(ApiResponse.success("学习进度更新成功", "Progress updated"));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("更新学习进度失败", e.getMessage()));
        }
    }

    /**
     * 保存学习书签
     */
    @PostMapping("/bookmark")
    @PreAuthorize("hasRole('USER') or hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> saveBookmark(@RequestBody Map<String, Object> request) {
        try {
            String sessionId = (String) request.get("sessionId");
            String position = (String) request.get("position");
            String notes = (String) request.get("notes");
            
            learningService.saveBookmark(sessionId, position, notes);
            
            return ResponseEntity.ok(ApiResponse.success("书签保存成功", "Bookmark saved"));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("保存书签失败", e.getMessage()));
        }
    }

    /**
     * 完成学习
     */
    @PostMapping("/complete")
    @PreAuthorize("hasRole('USER') or hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> completeLearning(@RequestBody Map<String, Object> request) {
        try {
            String sessionId = (String) request.get("sessionId");
            Integer finalProgress = (Integer) request.get("finalProgress");
            Integer totalTimeSpent = (Integer) request.get("totalTimeSpent");
            String finalPosition = (String) request.get("finalPosition");
            
            Map<String, Object> result = learningService.endLearningSession(sessionId, finalProgress, totalTimeSpent, finalPosition);
            
            return ResponseEntity.ok(ApiResponse.success("学习完成", result));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("完成学习失败", e.getMessage()));
        }
    }

    /**
     * 结束学习会话
     */
    @PostMapping("/end")
    @PreAuthorize("hasRole('USER') or hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> endLearningSession(@RequestBody Map<String, Object> request) {
        try {
            String sessionId = (String) request.get("sessionId");
            Integer finalProgress = (Integer) request.get("finalProgress");
            Integer totalTimeSpent = (Integer) request.get("totalTimeSpent");
            String finalPosition = (String) request.get("finalPosition");
            
            Map<String, Object> result = learningService.endLearningSession(sessionId, finalProgress, totalTimeSpent, finalPosition);
            
            return ResponseEntity.ok(ApiResponse.success("学习会话结束成功", result));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("结束学习会话失败", e.getMessage()));
        }
    }

    /**
     * 心跳检测
     */
    @PostMapping("/heartbeat")
    @PreAuthorize("hasRole('USER') or hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> heartbeat(@RequestBody Map<String, Object> request) {
        try {
            String sessionId = (String) request.get("sessionId");
            learningService.heartbeat(sessionId);
            
            return ResponseEntity.ok(ApiResponse.success("心跳成功", "Heartbeat received"));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("心跳失败", e.getMessage()));
        }
    }

    /**
     * 记录用户交互
     */
    @PostMapping("/interaction")
    @PreAuthorize("hasRole('USER') or hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> recordInteraction(@RequestBody Map<String, Object> request) {
        try {
            String sessionId = (String) request.get("sessionId");
            String interactionType = (String) request.get("interactionType");
            
            learningService.recordInteraction(sessionId, interactionType);
            
            return ResponseEntity.ok(ApiResponse.success("交互记录成功", "Interaction recorded"));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("记录交互失败", e.getMessage()));
        }
    }

    /**
     * 获取学习进度
     */
    @GetMapping("/progress/{courseId}")
    @PreAuthorize("hasRole('USER') or hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getLearningProgress(
            @PathVariable Long courseId,
            Authentication authentication) {
        
        try {
            String username = authentication.getName();
            User user = userService.getUserByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> progress = learningService.getLearningProgress(courseId, user.getId());
            
            return ResponseEntity.ok(ApiResponse.success("获取学习进度成功", progress));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取学习进度失败", e.getMessage()));
        }
    }

    /**
     * 获取学习统计
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('USER') or hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getLearningStatistics(Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.getUserByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> statistics = learningService.getLearningStatistics(user.getId());
            
            return ResponseEntity.ok(ApiResponse.success("获取学习统计成功", statistics));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取学习统计失败", e.getMessage()));
        }
    }

    /**
     * 计算学习效率
     */
    @GetMapping("/efficiency/{courseId}")
    @PreAuthorize("hasRole('USER') or hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Double>> calculateLearningEfficiency(
            @PathVariable Long courseId,
            Authentication authentication) {
        
        try {
            String username = authentication.getName();
            User user = userService.getUserByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Double efficiency = learningService.calculateLearningEfficiency(courseId, user.getId());
            
            return ResponseEntity.ok(ApiResponse.success("学习效率计算成功", efficiency));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("计算学习效率失败", e.getMessage()));
        }
    }

    /**
     * 管理员获取所有学习统计（仅管理员）
     */
    @GetMapping("/admin/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAdminStatistics() {
        try {
            // TODO: 实现管理员统计功能
            Map<String, Object> adminStats = Map.of(
                "message", "管理员统计功能待实现"
            );
            
            return ResponseEntity.ok(ApiResponse.success("获取管理员统计成功", adminStats));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取管理员统计失败", e.getMessage()));
        }
    }

    /**
     * 获取课程学习分析（仅管理员和QA）
     */
    @GetMapping("/admin/course/{courseId}/analytics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCourseAnalytics(@PathVariable Long courseId) {
        try {
            // TODO: 实现课程分析功能
            Map<String, Object> analytics = Map.of(
                "courseId", courseId,
                "message", "课程分析功能待实现"
            );
            
            return ResponseEntity.ok(ApiResponse.success("获取课程分析成功", analytics));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取课程分析失败", e.getMessage()));
        }
    }
}
