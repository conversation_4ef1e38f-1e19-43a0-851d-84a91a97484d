<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单文件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        input[type="file"], input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🧪 DMS文件上传功能测试</h1>
    
    <div class="test-section">
        <h3>1. 认证状态检查</h3>
        <button class="btn-primary" onclick="checkAuth()">检查认证状态</button>
        <div id="authStatus"></div>
    </div>

    <div class="test-section">
        <h3>2. 简单文件上传测试</h3>
        <form id="uploadForm">
            <label>选择文件:</label>
            <input type="file" id="fileInput" name="file" required>
            
            <label>文档标题:</label>
            <input type="text" id="titleInput" name="title" placeholder="输入文档标题" required>
            
            <label>描述 (可选):</label>
            <input type="text" id="descInput" name="description" placeholder="输入文档描述">
            
            <button type="submit" class="btn-success">上传文件</button>
        </form>
    </div>

    <div class="test-section">
        <h3>3. 测试日志</h3>
        <button class="btn-danger" onclick="clearLog()">清空日志</button>
        <div id="testLog" class="log"></div>
    </div>

    <script src="/dms/js/auth.js"></script>
    <script>
        let logElement = document.getElementById('testLog');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            logElement.innerHTML = '';
        }

        async function checkAuth() {
            log('=== 检查认证状态 ===', 'info');
            
            try {
                // 初始化认证
                authUtils.initAuth();
                
                if (authUtils.isLoggedIn()) {
                    const user = authUtils.getCurrentUser();
                    log(`✅ 用户已登录: ${user.username} (${user.roles.join(', ')})`, 'success');
                    document.getElementById('authStatus').innerHTML = 
                        `<span class="success">✅ 已登录: ${user.username}</span>`;
                } else {
                    log('❌ 用户未登录', 'error');
                    document.getElementById('authStatus').innerHTML = 
                        '<span class="error">❌ 未登录 - 请先登录系统</span>';
                }
            } catch (error) {
                log(`❌ 认证检查失败: ${error.message}`, 'error');
                document.getElementById('authStatus').innerHTML = 
                    `<span class="error">❌ 认证检查失败: ${error.message}</span>`;
            }
        }

        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            log('=== 开始文件上传测试 ===', 'info');
            
            const fileInput = document.getElementById('fileInput');
            const titleInput = document.getElementById('titleInput');
            const descInput = document.getElementById('descInput');
            
            // 验证输入
            if (!fileInput.files[0]) {
                log('❌ 未选择文件', 'error');
                return;
            }
            
            if (!titleInput.value.trim()) {
                log('❌ 标题不能为空', 'error');
                return;
            }
            
            const file = fileInput.files[0];
            log(`📁 文件信息:`, 'info');
            log(`   - 文件名: ${file.name}`, 'info');
            log(`   - 文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB`, 'info');
            log(`   - 文件类型: ${file.type}`, 'info');
            log(`   - 标题: ${titleInput.value}`, 'info');
            
            // 检查认证
            if (!authUtils.isLoggedIn()) {
                log('❌ 用户未登录，无法上传', 'error');
                return;
            }
            
            try {
                // 准备FormData
                const formData = new FormData();
                formData.append('file', file);
                formData.append('title', titleInput.value.trim());
                if (descInput.value.trim()) {
                    formData.append('description', descInput.value.trim());
                }
                
                log('📦 FormData准备完成', 'info');
                
                // 发送上传请求
                log('🌐 发送上传请求...', 'info');
                const response = await authUtils.secureApiCall('/dms/api/documents/upload', {
                    method: 'POST',
                    body: formData
                });
                
                log(`📨 收到响应: ${response ? response.status : 'null'}`, 'info');
                
                if (response && response.ok) {
                    const result = await response.json();
                    log('✅ 上传成功!', 'success');
                    log(`   - 文档ID: ${result.data.id}`, 'success');
                    log(`   - 文档标题: ${result.data.title}`, 'success');
                    log(`   - 文件名: ${result.data.fileName}`, 'success');
                    
                    // 重置表单
                    this.reset();
                    
                } else if (response) {
                    const errorData = await response.json();
                    log(`❌ 上传失败: ${errorData.message || errorData.error}`, 'error');
                    log(`   - HTTP状态: ${response.status}`, 'error');
                } else {
                    log('❌ 网络连接失败', 'error');
                }
                
            } catch (error) {
                log(`💥 上传异常: ${error.message}`, 'error');
                console.error('Upload error:', error);
            }
        });

        // 页面加载时自动检查认证状态
        window.addEventListener('load', function() {
            log('🚀 页面加载完成，开始测试...', 'info');
            checkAuth();
        });
    </script>
</body>
</html>
