package com.pharma.dms.service;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pharma.dms.entity.DocumentCategory;
import com.pharma.dms.repository.DocumentCategoryRepository;

@Service
@Transactional
public class DocumentCategoryService {

    @Autowired
    private DocumentCategoryRepository categoryRepository;

    @Autowired
    private AuditService auditService;

    public List<DocumentCategory> getAllCategories() {
        return categoryRepository.findAllOrderBySortOrder();
    }

    public List<DocumentCategory> getActiveCategories() {
        return categoryRepository.findActiveOrderBySortOrder();
    }

    public List<DocumentCategory> getRootCategories() {
        return categoryRepository.findRootCategories();
    }

    public List<DocumentCategory> getSubCategories(Long parentId) {
        return categoryRepository.findByParentCategoryId(parentId);
    }

    public Optional<DocumentCategory> getCategoryById(Long id) {
        return categoryRepository.findById(id);
    }

    public Optional<DocumentCategory> getCategoryByCode(String code) {
        return categoryRepository.findByCode(code);
    }

    @Transactional
    public DocumentCategory createCategory(DocumentCategory category) {
        try {
            // 验证输入参数
            if (category == null) {
                throw new IllegalArgumentException("Category cannot be null");
            }

            if (category.getName() == null || category.getName().trim().isEmpty()) {
                throw new IllegalArgumentException("Category name cannot be empty");
            }

            // 检查名称重复
            if (categoryRepository.existsByName(category.getName())) {
                throw new IllegalStateException("Category name already exists: " + category.getName());
            }

            // 检查代码重复
            if (category.getCode() != null && categoryRepository.existsByCode(category.getCode())) {
                throw new IllegalStateException("Category code already exists: " + category.getCode());
            }

            DocumentCategory savedCategory = categoryRepository.save(category);

            // 记录审计日志
            auditService.logSystemEvent("CATEGORY_CREATED",
                    "Document category created: " + category.getName(),
                    com.pharma.dms.entity.AuditLog.Severity.INFO);

            return savedCategory;
        } catch (IllegalArgumentException | IllegalStateException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            // 记录系统错误并抛出运行时异常
            auditService.logSystemEvent("CATEGORY_CREATE_ERROR",
                    "Failed to create category: " + e.getMessage(),
                    com.pharma.dms.entity.AuditLog.Severity.ERROR);
            throw new RuntimeException("Failed to create document category", e);
        }
    }

    public DocumentCategory updateCategory(Long id, DocumentCategory categoryDetails) {
        DocumentCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Category not found"));

        String oldValues = category.toString();

        category.setName(categoryDetails.getName());
        category.setDescription(categoryDetails.getDescription());
        category.setCode(categoryDetails.getCode());
        category.setColor(categoryDetails.getColor());
        category.setIcon(categoryDetails.getIcon());
        category.setIsActive(categoryDetails.getIsActive());
        category.setSortOrder(categoryDetails.getSortOrder());
        category.setMaxFileSizeMb(categoryDetails.getMaxFileSizeMb());
        category.setAllowedFileTypes(categoryDetails.getAllowedFileTypes());
        category.setRequiresApproval(categoryDetails.getRequiresApproval());
        category.setRetentionPeriodMonths(categoryDetails.getRetentionPeriodMonths());

        DocumentCategory updatedCategory = categoryRepository.save(category);

        auditService.logSystemEvent("CATEGORY_UPDATED", 
                "Document category updated: " + category.getName(), 
                com.pharma.dms.entity.AuditLog.Severity.INFO);

        return updatedCategory;
    }

    public void deleteCategory(Long id) {
        DocumentCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Category not found"));

        // Check if category has documents
        Long documentCount = categoryRepository.countDocumentsByCategoryId(id);
        if (documentCount > 0) {
            throw new RuntimeException("Cannot delete category with existing documents");
        }

        // Soft delete - just deactivate
        category.setIsActive(false);
        categoryRepository.save(category);

        auditService.logSystemEvent("CATEGORY_DELETED", 
                "Document category deleted: " + category.getName(), 
                com.pharma.dms.entity.AuditLog.Severity.WARNING);
    }

    public long getDocumentCount(Long categoryId) {
        return categoryRepository.countDocumentsByCategoryId(categoryId);
    }
}
