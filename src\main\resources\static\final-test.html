<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS系统最终验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .result-pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .result-fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .result-pending { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <h1>🧪 DMS系统最终验证测试</h1>
    
    <div class="test-section">
        <h3>测试概览</h3>
        <button class="btn-success" onclick="runAllTests()">运行完整测试</button>
        <button class="btn-primary" onclick="runCriticalTests()">运行关键测试</button>
        <button class="btn-danger" onclick="clearLog()">清空日志</button>
        
        <div id="testResults">
            <div id="result-login" class="test-result result-pending">🔐 登录功能: 待测试</div>
            <div id="result-dashboard" class="test-result result-pending">📊 仪表板数据: 待测试</div>
            <div id="result-users" class="test-result result-pending">👥 用户管理: 待测试</div>
            <div id="result-documents" class="test-result result-pending">📄 文档管理: 待测试</div>
            <div id="result-upload" class="test-result result-pending">📤 文件上传: 待测试</div>
        </div>
    </div>

    <div class="test-section">
        <h3>快速文件上传测试</h3>
        <input type="file" id="quickTestFile" accept=".pdf,.doc,.docx,.txt">
        <input type="text" id="quickTestTitle" placeholder="文档标题" value="快速测试文档">
        <button class="btn-warning" onclick="quickUploadTest()">快速上传测试</button>
    </div>

    <div class="test-section">
        <h3>测试日志</h3>
        <div id="testLog" class="log"></div>
    </div>

    <script>
        let logElement = document.getElementById('testLog');
        let testToken = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            logElement.innerHTML = '';
        }

        function updateTestResult(testId, status, message) {
            const element = document.getElementById(`result-${testId}`);
            element.className = `test-result result-${status}`;
            
            const icons = { pass: '✅', fail: '❌', pending: '⏳' };
            const statusText = { pass: '通过', fail: '失败', pending: '待测试' };
            
            element.textContent = `${icons[status]} ${message}: ${statusText[status]}`;
        }

        async function testLogin() {
            log('=== 测试登录功能 ===', 'info');
            updateTestResult('login', 'pending', '登录功能');
            
            try {
                const response = await fetch('/dms/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });

                log(`登录响应状态: ${response.status}`, 'info');
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data && result.data.token) {
                        testToken = result.data.token;
                        log('✅ 登录成功，获取到token', 'success');
                        updateTestResult('login', 'pass', '登录功能');
                        return true;
                    } else {
                        log(`❌ 登录失败: ${result.message}`, 'error');
                        updateTestResult('login', 'fail', '登录功能');
                        return false;
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ 登录请求失败: ${response.status} - ${errorText}`, 'error');
                    updateTestResult('login', 'fail', '登录功能');
                    return false;
                }
            } catch (error) {
                log(`💥 登录异常: ${error.message}`, 'error');
                updateTestResult('login', 'fail', '登录功能');
                return false;
            }
        }

        async function testDashboard() {
            log('=== 测试仪表板数据 ===', 'info');
            updateTestResult('dashboard', 'pending', '仪表板数据');
            
            if (!testToken) {
                log('❌ 没有token，跳过仪表板测试', 'error');
                updateTestResult('dashboard', 'fail', '仪表板数据');
                return false;
            }

            try {
                const response = await fetch('/dms/api/dashboard/stats', {
                    headers: {
                        'Authorization': 'Bearer ' + testToken,
                        'Content-Type': 'application/json'
                    }
                });

                log(`仪表板响应状态: ${response.status}`, 'info');
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data) {
                        log('✅ 仪表板数据加载成功', 'success');
                        log(`   - 总文档数: ${result.data.totalDocuments || 0}`, 'info');
                        log(`   - 活跃用户: ${result.data.activeUsers || 0}`, 'info');
                        updateTestResult('dashboard', 'pass', '仪表板数据');
                        return true;
                    } else {
                        log(`❌ 仪表板数据格式错误: ${result.message}`, 'error');
                        updateTestResult('dashboard', 'fail', '仪表板数据');
                        return false;
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ 仪表板请求失败: ${response.status} - ${errorText}`, 'error');
                    updateTestResult('dashboard', 'fail', '仪表板数据');
                    return false;
                }
            } catch (error) {
                log(`💥 仪表板异常: ${error.message}`, 'error');
                updateTestResult('dashboard', 'fail', '仪表板数据');
                return false;
            }
        }

        async function testUsers() {
            log('=== 测试用户管理 ===', 'info');
            updateTestResult('users', 'pending', '用户管理');
            
            if (!testToken) {
                log('❌ 没有token，跳过用户测试', 'error');
                updateTestResult('users', 'fail', '用户管理');
                return false;
            }

            try {
                const response = await fetch('/dms/api/users', {
                    headers: {
                        'Authorization': 'Bearer ' + testToken,
                        'Content-Type': 'application/json'
                    }
                });

                log(`用户列表响应状态: ${response.status}`, 'info');
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data) {
                        const users = Array.isArray(result.data) ? result.data : result.data.content;
                        log(`✅ 用户列表加载成功，共 ${users ? users.length : 0} 个用户`, 'success');
                        updateTestResult('users', 'pass', '用户管理');
                        return true;
                    } else {
                        log(`❌ 用户数据格式错误: ${result.message}`, 'error');
                        updateTestResult('users', 'fail', '用户管理');
                        return false;
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ 用户列表请求失败: ${response.status} - ${errorText}`, 'error');
                    updateTestResult('users', 'fail', '用户管理');
                    return false;
                }
            } catch (error) {
                log(`💥 用户管理异常: ${error.message}`, 'error');
                updateTestResult('users', 'fail', '用户管理');
                return false;
            }
        }

        async function testDocuments() {
            log('=== 测试文档管理 ===', 'info');
            updateTestResult('documents', 'pending', '文档管理');
            
            if (!testToken) {
                log('❌ 没有token，跳过文档测试', 'error');
                updateTestResult('documents', 'fail', '文档管理');
                return false;
            }

            try {
                const response = await fetch('/dms/api/documents?page=0&size=5', {
                    headers: {
                        'Authorization': 'Bearer ' + testToken,
                        'Content-Type': 'application/json'
                    }
                });

                log(`文档列表响应状态: ${response.status}`, 'info');
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data) {
                        const docs = result.data.content || result.data;
                        log(`✅ 文档列表加载成功，共 ${docs ? docs.length : 0} 个文档`, 'success');
                        updateTestResult('documents', 'pass', '文档管理');
                        return true;
                    } else {
                        log(`❌ 文档数据格式错误: ${result.message}`, 'error');
                        updateTestResult('documents', 'fail', '文档管理');
                        return false;
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ 文档列表请求失败: ${response.status} - ${errorText}`, 'error');
                    updateTestResult('documents', 'fail', '文档管理');
                    return false;
                }
            } catch (error) {
                log(`💥 文档管理异常: ${error.message}`, 'error');
                updateTestResult('documents', 'fail', '文档管理');
                return false;
            }
        }

        async function quickUploadTest() {
            log('=== 快速文件上传测试 ===', 'info');
            updateTestResult('upload', 'pending', '文件上传');
            
            const fileInput = document.getElementById('quickTestFile');
            const titleInput = document.getElementById('quickTestTitle');
            
            if (!fileInput.files[0]) {
                log('❌ 请选择文件', 'error');
                updateTestResult('upload', 'fail', '文件上传');
                return false;
            }
            
            if (!testToken) {
                log('❌ 没有token，请先运行登录测试', 'error');
                updateTestResult('upload', 'fail', '文件上传');
                return false;
            }
            
            const file = fileInput.files[0];
            const title = titleInput.value.trim() || file.name;
            
            log(`📁 文件: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`, 'info');
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('title', title);
            formData.append('description', '快速测试上传');
            
            try {
                const response = await fetch('/dms/api/documents/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + testToken
                    },
                    body: formData
                });

                log(`上传响应状态: ${response.status}`, 'info');
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data) {
                        log(`✅ 文件上传成功: ${result.data.title}`, 'success');
                        updateTestResult('upload', 'pass', '文件上传');
                        return true;
                    } else {
                        log(`❌ 上传响应格式错误: ${result.message}`, 'error');
                        updateTestResult('upload', 'fail', '文件上传');
                        return false;
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ 文件上传失败: ${response.status} - ${errorText}`, 'error');
                    updateTestResult('upload', 'fail', '文件上传');
                    return false;
                }
            } catch (error) {
                log(`💥 文件上传异常: ${error.message}`, 'error');
                updateTestResult('upload', 'fail', '文件上传');
                return false;
            }
        }

        async function runCriticalTests() {
            log('🚀 开始运行关键测试...', 'info');
            clearLog();
            
            const loginSuccess = await testLogin();
            if (loginSuccess) {
                await testDashboard();
                await testDocuments();
            }
            
            log('🏁 关键测试完成', 'info');
        }

        async function runAllTests() {
            log('🚀 开始运行完整测试...', 'info');
            clearLog();
            
            const loginSuccess = await testLogin();
            if (loginSuccess) {
                await testDashboard();
                await testUsers();
                await testDocuments();
            }
            
            log('🏁 完整测试完成', 'info');
        }

        // 页面加载时自动运行关键测试
        window.addEventListener('load', function() {
            log('🚀 最终验证测试页面加载完成', 'info');
            setTimeout(runCriticalTests, 1000);
        });
    </script>
</body>
</html>
