@echo off
echo Starting Pharmaceutical DMS System...

REM Kill existing Java processes
taskkill /F /IM java.exe 2>nul

REM Start PostgreSQL
echo Starting PostgreSQL...
D:\sql\pgsql\bin\pg_ctl.exe -D D:\sql\pgsql\data start

REM Wait for PostgreSQL to start
timeout /t 5 /nobreak >nul

REM Start DMS application with correct Maven syntax
echo Starting DMS application...
cd /d D:\learn\java\dms
set SPRING_PROFILES_ACTIVE=simple
mvn spring-boot:run -Dspring-boot.run.profiles=simple

pause
