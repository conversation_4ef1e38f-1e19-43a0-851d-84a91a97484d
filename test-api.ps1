# API测试脚本
$baseUrl = "http://localhost:8081/dms"

Write-Host "=== DMS API测试脚本 ===" -ForegroundColor Green

# 1. 测试基础API
Write-Host "`n1. 测试基础API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/test/basic" -Method GET
    Write-Host "✅ 基础API状态: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 基础API失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 登录获取token
Write-Host "`n2. 登录获取token..." -ForegroundColor Yellow
$loginData = @{
    username = "admin"
    password = "admin123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-WebRequest -Uri "$baseUrl/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    $loginResult = $loginResponse.Content | ConvertFrom-Json
    
    if ($loginResult.success) {
        $token = $loginResult.data.token
        Write-Host "✅ 登录成功，获取到token" -ForegroundColor Green
        Write-Host "Token前缀: $($token.Substring(0, 50))..." -ForegroundColor Cyan
        
        # 3. 测试需要认证的API
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        # 测试仪表板统计API
        Write-Host "`n3. 测试仪表板统计API..." -ForegroundColor Yellow
        try {
            $statsResponse = Invoke-WebRequest -Uri "$baseUrl/api/dashboard/stats" -Method GET -Headers $headers
            Write-Host "✅ 仪表板统计API状态: $($statsResponse.StatusCode)" -ForegroundColor Green
            Write-Host "响应内容: $($statsResponse.Content)" -ForegroundColor Cyan
        } catch {
            Write-Host "❌ 仪表板统计API失败: $($_.Exception.Message)" -ForegroundColor Red
            if ($_.Exception.Response) {
                $errorStream = $_.Exception.Response.GetResponseStream()
                $reader = New-Object System.IO.StreamReader($errorStream)
                $errorContent = $reader.ReadToEnd()
                Write-Host "错误响应: $errorContent" -ForegroundColor Red
            }
        }
        
        # 测试用户列表API
        Write-Host "`n4. 测试用户列表API..." -ForegroundColor Yellow
        try {
            $usersResponse = Invoke-WebRequest -Uri "$baseUrl/api/users" -Method GET -Headers $headers
            Write-Host "✅ 用户列表API状态: $($usersResponse.StatusCode)" -ForegroundColor Green
            $usersContent = $usersResponse.Content
            if ($usersContent.Length -gt 500) {
                Write-Host "响应内容: $($usersContent.Substring(0, 500))..." -ForegroundColor Cyan
            } else {
                Write-Host "响应内容: $usersContent" -ForegroundColor Cyan
            }
        } catch {
            Write-Host "❌ 用户列表API失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # 测试文档列表API
        Write-Host "`n5. 测试文档列表API..." -ForegroundColor Yellow
        try {
            $docsResponse = Invoke-WebRequest -Uri "$baseUrl/api/documents?page=0&size=5" -Method GET -Headers $headers
            Write-Host "✅ 文档列表API状态: $($docsResponse.StatusCode)" -ForegroundColor Green
            $docsContent = $docsResponse.Content
            if ($docsContent.Length -gt 500) {
                Write-Host "响应内容: $($docsContent.Substring(0, 500))..." -ForegroundColor Cyan
            } else {
                Write-Host "响应内容: $docsContent" -ForegroundColor Cyan
            }
        } catch {
            Write-Host "❌ 文档列表API失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
    } else {
        Write-Host "❌ 登录失败: $($loginResult.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
