/**
 * 培训分配管理器
 * 负责培训分配的前端交互逻辑
 */
class TrainingAssignmentManager {
    constructor() {
        this.selectedUsers = new Set();
        this.allUsers = [];
        this.allCourses = [];
        this.allDepartments = [];
        this.currentPage = 0;
        this.pageSize = 12;
        this.isLoading = false;
        
        this.init();
    }

    init() {
        console.log('=== 培训分配管理器初始化 ===');
        this.bindEvents();
        this.loadInitialData();
    }

    bindEvents() {
        // 分配表单提交
        const assignmentForm = document.getElementById('assignmentForm');
        if (assignmentForm) {
            assignmentForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.executeAssignment();
            });
        }

        // 课程选择变化
        const courseSelect = document.getElementById('courseSelect');
        if (courseSelect) {
            courseSelect.addEventListener('change', (e) => this.onCourseChange(e));
        }

        // 分配类型变化
        const assignmentTypeRadios = document.querySelectorAll('input[name="assignmentType"]');
        assignmentTypeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => this.onAssignmentTypeChange(e));
        });
    }

    // ========== 数据加载 ==========

    async loadInitialData() {
        this.showLoadingState();
        
        try {
            await Promise.all([
                this.loadUsers(),
                this.loadCourses(),
                this.loadDepartments()
            ]);
            
            this.updateUI();
        } catch (error) {
            console.error('❌ 初始数据加载失败:', error);
            this.showNotification('数据加载失败: ' + error.message, 'error');
        } finally {
            this.hideLoadingState();
        }
    }

    async loadUsers() {
        try {
            const response = await authUtils.secureApiCall('/dms/api/users?page=0&size=1000');
            if (response.ok) {
                const result = await response.json();
                this.allUsers = result.data.content || result.data || [];
                console.log('✅ 用户数据加载成功:', this.allUsers.length);
            } else {
                throw new Error('用户数据加载失败');
            }
        } catch (error) {
            console.error('❌ 用户数据加载失败:', error);
            throw error;
        }
    }

    async loadCourses() {
        try {
            const response = await authUtils.secureApiCall('/dms/api/training/courses?status=ACTIVE');
            if (response.ok) {
                const result = await response.json();
                this.allCourses = result.data.content || result.data || [];
                console.log('✅ 课程数据加载成功:', this.allCourses.length);
            } else {
                throw new Error('课程数据加载失败');
            }
        } catch (error) {
            console.error('❌ 课程数据加载失败:', error);
            throw error;
        }
    }

    async loadDepartments() {
        try {
            const response = await authUtils.secureApiCall('/dms/api/departments');
            if (response.ok) {
                const result = await response.json();
                this.allDepartments = result.data || [];
                console.log('✅ 部门数据加载成功:', this.allDepartments.length);
            } else {
                throw new Error('部门数据加载失败');
            }
        } catch (error) {
            console.error('❌ 部门数据加载失败:', error);
            throw error;
        }
    }

    // ========== UI更新 ==========

    updateUI() {
        this.populateCourseSelect();
        this.populateDepartmentFilter();
        this.displayUsers();
        this.updateSelectedCount();
    }

    populateCourseSelect() {
        const courseSelect = document.getElementById('courseSelect');
        if (!courseSelect) return;

        courseSelect.innerHTML = '<option value="">请选择课程</option>';
        
        this.allCourses.forEach(course => {
            const option = document.createElement('option');
            option.value = course.id;
            option.textContent = `${course.title} (${course.courseCode})`;
            option.dataset.course = JSON.stringify(course);
            courseSelect.appendChild(option);
        });
    }

    populateDepartmentFilter() {
        const departmentFilter = document.getElementById('departmentFilter');
        if (!departmentFilter) return;

        // 保留"所有部门"选项
        departmentFilter.innerHTML = '<option value="">所有部门</option>';
        
        this.allDepartments.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept.id;
            option.textContent = dept.name;
            departmentFilter.appendChild(option);
        });
    }

    displayUsers() {
        const usersList = document.getElementById('usersList');
        if (!usersList) return;

        const filteredUsers = this.getFilteredUsers();
        const startIndex = this.currentPage * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageUsers = filteredUsers.slice(startIndex, endIndex);

        if (pageUsers.length === 0) {
            usersList.innerHTML = this.getEmptyStateHtml();
            return;
        }

        const usersHtml = pageUsers.map(user => this.createUserCard(user)).join('');
        usersList.innerHTML = usersHtml;

        this.updatePagination(filteredUsers.length);
    }

    createUserCard(user) {
        const isSelected = this.selectedUsers.has(user.id);
        const avatar = this.getUserAvatar(user);
        const department = user.department ? user.department.name : '未分配';
        const roles = user.roles ? user.roles.map(r => r.name.replace('ROLE_', '')).join(', ') : '无角色';

        return `
            <div class="col-md-4 col-lg-3 mb-3">
                <div class="user-card ${isSelected ? 'selected' : ''}" onclick="trainingAssignmentManager.toggleUserSelection(${user.id})">
                    <div class="d-flex align-items-center mb-2">
                        <div class="user-avatar me-3">${avatar}</div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0">${this.escapeHtml(user.firstName + ' ' + user.lastName)}</h6>
                            <small class="text-muted">@${this.escapeHtml(user.username)}</small>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" ${isSelected ? 'checked' : ''} 
                                   onchange="event.stopPropagation(); trainingAssignmentManager.toggleUserSelection(${user.id})">
                        </div>
                    </div>
                    <div class="user-details">
                        <div class="mb-1">
                            <i class="fas fa-building text-muted me-1"></i>
                            <small>${this.escapeHtml(department)}</small>
                        </div>
                        <div class="mb-1">
                            <i class="fas fa-user-tag text-muted me-1"></i>
                            <small>${this.escapeHtml(roles)}</small>
                        </div>
                        <div class="mb-1">
                            <i class="fas fa-envelope text-muted me-1"></i>
                            <small>${this.escapeHtml(user.email)}</small>
                        </div>
                        <div>
                            <span class="badge ${user.isActive ? 'bg-success' : 'bg-secondary'}">
                                ${user.isActive ? '活跃' : '禁用'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getUserAvatar(user) {
        const firstName = user.firstName || '';
        const lastName = user.lastName || '';
        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
    }

    getFilteredUsers() {
        const departmentFilter = document.getElementById('departmentFilter')?.value;
        const roleFilter = document.getElementById('roleFilter')?.value;
        const statusFilter = document.getElementById('statusFilter')?.value;
        const searchInput = document.getElementById('searchInput')?.value?.toLowerCase();

        return this.allUsers.filter(user => {
            // 部门过滤
            if (departmentFilter && user.department?.id != departmentFilter) {
                return false;
            }

            // 角色过滤
            if (roleFilter && !user.roles?.some(role => role.name.includes(roleFilter))) {
                return false;
            }

            // 状态过滤
            if (statusFilter === 'active' && !user.isActive) {
                return false;
            }
            if (statusFilter === 'inactive' && user.isActive) {
                return false;
            }

            // 搜索过滤
            if (searchInput) {
                const fullName = (user.firstName + ' ' + user.lastName).toLowerCase();
                const username = user.username.toLowerCase();
                if (!fullName.includes(searchInput) && !username.includes(searchInput)) {
                    return false;
                }
            }

            return true;
        });
    }

    // ========== 用户选择 ==========

    toggleUserSelection(userId) {
        if (this.selectedUsers.has(userId)) {
            this.selectedUsers.delete(userId);
        } else {
            this.selectedUsers.add(userId);
        }
        
        this.updateSelectedCount();
        this.updateUserCardSelection(userId);
    }

    updateUserCardSelection(userId) {
        const userCard = document.querySelector(`[onclick*="${userId}"]`);
        if (userCard) {
            const isSelected = this.selectedUsers.has(userId);
            userCard.classList.toggle('selected', isSelected);
            
            const checkbox = userCard.querySelector('input[type="checkbox"]');
            if (checkbox) {
                checkbox.checked = isSelected;
            }
        }
    }

    updateSelectedCount() {
        const countElement = document.getElementById('selectedCount');
        if (countElement) {
            countElement.textContent = this.selectedUsers.size;
        }
    }

    selectAll() {
        const filteredUsers = this.getFilteredUsers();
        filteredUsers.forEach(user => this.selectedUsers.add(user.id));
        this.displayUsers();
        this.updateSelectedCount();
    }

    selectNone() {
        this.selectedUsers.clear();
        this.displayUsers();
        this.updateSelectedCount();
    }

    // ========== 分配执行 ==========

    async executeAssignment() {
        if (this.selectedUsers.size === 0) {
            this.showNotification('请至少选择一个用户', 'warning');
            return;
        }

        const courseId = document.getElementById('courseSelect')?.value;
        if (!courseId) {
            this.showNotification('请选择要分配的课程', 'warning');
            return;
        }

        const assignmentType = document.querySelector('input[name="assignmentType"]:checked')?.value;
        const dueDate = document.getElementById('dueDate')?.value;
        const notes = document.getElementById('assignmentNotes')?.value;
        const reason = document.getElementById('assignmentReason')?.value;

        try {
            this.showLoadingState();

            const requestData = {
                courseId: parseInt(courseId),
                userIds: Array.from(this.selectedUsers),
                dueDate: dueDate || null,
                notes: notes || '',
                reason: reason || '手动分配'
            };

            console.log('=== 执行培训分配 ===');
            console.log('分配数据:', requestData);

            const response = await authUtils.secureApiCall('/dms/api/training/assignments/assign/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 分配成功:', result);
                
                this.showNotification(
                    `培训分配完成！成功分配 ${result.data.assignedCount} 人，共 ${result.data.totalRequested} 人`, 
                    'success'
                );
                
                this.resetAssignmentForm();
                this.closeAssignmentModal();
            } else {
                const error = await response.json();
                throw new Error(error.message || '分配失败');
            }
        } catch (error) {
            console.error('❌ 分配失败:', error);
            this.showNotification('分配失败: ' + error.message, 'error');
        } finally {
            this.hideLoadingState();
        }
    }

    // ========== 工具方法 ==========

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    getEmptyStateHtml() {
        return `
            <div class="col-12 text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">没有找到符合条件的用户</h5>
                <p class="text-muted">请调整筛选条件或搜索关键词</p>
            </div>
        `;
    }

    showLoadingState() {
        // TODO: 实现加载状态显示
        this.isLoading = true;
    }

    hideLoadingState() {
        // TODO: 实现加载状态隐藏
        this.isLoading = false;
    }

    showNotification(message, type) {
        // TODO: 实现通知显示
        console.log(`${type.toUpperCase()}: ${message}`);
        alert(message);
    }

    updatePagination(totalItems) {
        // TODO: 实现分页更新
    }

    resetAssignmentForm() {
        document.getElementById('assignmentForm')?.reset();
        this.selectedUsers.clear();
        this.updateSelectedCount();
    }

    closeAssignmentModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('assignmentModal'));
        if (modal) modal.hide();
    }

    // 事件处理方法
    onCourseChange(event) {
        const selectedOption = event.target.selectedOptions[0];
        if (selectedOption && selectedOption.dataset.course) {
            const course = JSON.parse(selectedOption.dataset.course);
            console.log('选择的课程:', course);
            // 可以在这里更新UI显示课程信息
        }
    }

    onAssignmentTypeChange(event) {
        const assignmentType = event.target.value;
        console.log('分配类型变更:', assignmentType);
        // 根据分配类型调整UI
    }

    // 全局方法（供HTML调用）
    filterUsers() {
        this.currentPage = 0;
        this.displayUsers();
    }
}

// 全局实例
let trainingAssignmentManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    trainingAssignmentManager = new TrainingAssignmentManager();
});
