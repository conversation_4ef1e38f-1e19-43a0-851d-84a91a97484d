/**
 * 文档管理优化脚本
 * 专门处理文档上传、显示、搜索等功能的优化
 */

class DocumentManager {
    constructor() {
        this.currentPage = 0;
        this.pageSize = 10;
        this.isLoading = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        this.isUploading = false; // 添加上传状态标志
        this.buttonStates = new Map(); // 按钮状态管理
        this.logger = window.loggers?.document || console; // 使用统一日志系统

        this.init();
    }

    // HTML转义函数，防止XSS攻击
    escapeHtml(text) {
        if (typeof text !== 'string') return text;
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 安全地设置元素文本内容
    setTextContent(element, text) {
        if (element) {
            element.textContent = text || '';
        }
    }

    // 安全地设置元素属性
    setAttribute(element, name, value) {
        if (element && value) {
            element.setAttribute(name, this.escapeHtml(value));
        }
    }

    init() {
        this.logger.info('文档管理器初始化开始');
        this.bindEvents();
        this.loadDocuments();
        this.loadCategories();
        this.loadUsers();
    }

    bindEvents() {
        this.logger.info('绑定事件监听器开始');

        // 搜索表单事件
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.currentPage = 0;
                this.loadDocuments();
            });
            this.logger.info('搜索表单事件已绑定');
        }

        // 上传表单事件 - 确保只绑定一次
        this.bindUploadFormEvent();

        // 清除搜索按钮
        const clearBtn = document.getElementById('clearSearchBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearSearch());
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshDocuments());
        }

        // 模态框事件
        this.bindModalEvents();

        this.logger.info('所有事件监听器绑定完成');
    }

    bindUploadFormEvent() {
        const uploadForm = document.getElementById('uploadDocumentForm');
        if (uploadForm) {
            // 移除可能存在的旧事件监听器
            const newForm = uploadForm.cloneNode(true);
            uploadForm.parentNode.replaceChild(newForm, uploadForm);

            // 绑定新的事件监听器
            newForm.addEventListener('submit', (e) => this.handleUpload(e));
            this.logger.info('上传表单事件已绑定');
        } else {
            this.logger.warn('未找到上传表单，将在模态框显示时重试');
        }
    }

    bindModalEvents() {
        const uploadModal = document.getElementById('uploadDocumentModal');
        if (uploadModal) {
            // 模态框显示时重新绑定表单事件
            uploadModal.addEventListener('shown.bs.modal', () => {
                this.logger.info('上传模态框已显示，重新绑定表单事件');
                setTimeout(() => {
                    this.bindUploadFormEvent();
                    this.initializeDragAndDrop();
                }, 100);
            });

            // 模态框隐藏时重置表单
            uploadModal.addEventListener('hidden.bs.modal', () => {
                this.logger.info('上传模态框已隐藏，重置状态');
                this.isUploading = false;
                this.resetAllButtonStates();
                const form = document.getElementById('uploadDocumentForm');
                if (form) {
                    form.reset();
                }
                this.resetUploadProgress();
            });
        }
    }

    // 初始化拖拽上传功能
    initializeDragAndDrop() {
        const dropZone = document.getElementById('uploadDocumentModal');
        const fileInput = document.getElementById('documentFile');

        if (!dropZone || !fileInput) return;

        // 创建拖拽提示区域
        this.createDropZone();

        // 防止默认拖拽行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });

        // 拖拽进入和离开的视觉反馈
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => this.highlight(dropZone), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => this.unhighlight(dropZone), false);
        });

        // 处理文件拖放
        dropZone.addEventListener('drop', (e) => this.handleDrop(e, fileInput), false);
    }

    // 创建拖拽区域
    createDropZone() {
        const modalBody = document.querySelector('#uploadDocumentModal .modal-body');
        if (!modalBody || modalBody.querySelector('.drag-drop-zone')) return;

        const dropZone = document.createElement('div');
        dropZone.className = 'drag-drop-zone';
        dropZone.innerHTML = `
            <div class="drag-drop-content">
                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                <h5>拖拽文件到此处上传</h5>
                <p class="text-muted">或点击下方选择文件按钮</p>
                <div class="drag-drop-info">
                    <small class="text-muted">
                        支持：PDF, Word, Excel, 文本, 图片文件<br>
                        最大文件大小：1GB
                    </small>
                </div>
            </div>
        `;

        // 添加样式
        dropZone.style.cssText = `
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        `;

        modalBody.insertBefore(dropZone, modalBody.firstChild);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    highlight(element) {
        const dropZone = element.querySelector('.drag-drop-zone');
        if (dropZone) {
            dropZone.style.borderColor = '#007bff';
            dropZone.style.backgroundColor = '#e3f2fd';
        }
    }

    unhighlight(element) {
        const dropZone = element.querySelector('.drag-drop-zone');
        if (dropZone) {
            dropZone.style.borderColor = '#dee2e6';
            dropZone.style.backgroundColor = '#f8f9fa';
        }
    }

    handleDrop(e, fileInput) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            fileInput.files = files;
            this.logger.info('通过拖拽选择文件', { fileName: files[0].name, size: files[0].size });

            // 自动填充标题
            const titleInput = document.getElementById('documentTitle');
            if (titleInput && !titleInput.value) {
                const fileName = files[0].name;
                const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
                titleInput.value = nameWithoutExt;
            }

            // 显示文件信息
            this.displayFileInfo(files[0]);
        }
    }

    // 显示文件信息
    displayFileInfo(file) {
        const dropZone = document.querySelector('.drag-drop-zone');
        if (!dropZone) return;

        const fileSize = this.formatFileSize(file.size);
        const fileType = this.getFileTypeIcon(file.type);

        dropZone.innerHTML = `
            <div class="selected-file-info">
                <i class="${fileType} fa-2x text-primary mb-2"></i>
                <h6>${this.escapeHtml(file.name)}</h6>
                <p class="text-muted mb-0">大小: ${fileSize}</p>
                <small class="text-success">
                    <i class="fas fa-check-circle me-1"></i>文件已选择
                </small>
            </div>
        `;
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 获取文件类型图标
    getFileTypeIcon(mimeType) {
        if (mimeType.includes('pdf')) return 'fas fa-file-pdf';
        if (mimeType.includes('word') || mimeType.includes('document')) return 'fas fa-file-word';
        if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'fas fa-file-excel';
        if (mimeType.includes('image')) return 'fas fa-file-image';
        if (mimeType.includes('text')) return 'fas fa-file-alt';
        return 'fas fa-file';
    }

    async loadDocuments() {
        if (this.isLoading) {
            console.log('⏳ 正在加载中，跳过重复请求');
            return;
        }

        try {
            this.isLoading = true;
            this.showLoadingState();

            console.log('=== 加载文档列表 ===');
            console.log(`页码: ${this.currentPage}, 大小: ${this.pageSize}`);

            const searchParams = new URLSearchParams({
                page: this.currentPage,
                size: this.pageSize,
                sortBy: 'createdAt',
                sortDir: 'desc'
            });

            // 添加搜索条件
            this.addSearchParams(searchParams);

            const response = await authUtils.secureApiCall(`/dms/api/documents?${searchParams}`);

            if (response && response.ok) {
                const result = await response.json();
                console.log('✅ 文档加载成功:', result);

                this.displayDocuments(result.data.content);
                this.updatePagination(result.data);
                this.retryCount = 0; // 重置重试计数
            } else if (response) {
                const errorData = await response.json();
                throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
            } else {
                throw new Error('网络连接失败，请检查网络状态');
            }
        } catch (error) {
            console.error('❌ 加载文档失败:', error);
            this.handleLoadError(error);
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
        }
    }

    addSearchParams(searchParams) {
        const title = document.getElementById('searchTitle')?.value;
        const categoryId = document.getElementById('searchCategory')?.value;
        const status = document.getElementById('searchStatus')?.value;
        const ownerId = document.getElementById('searchOwner')?.value;

        if (title) searchParams.append('title', title);
        if (categoryId) searchParams.append('categoryId', categoryId);
        if (status) searchParams.append('status', status);
        if (ownerId) searchParams.append('ownerId', ownerId);
    }

    displayDocuments(documents) {
        console.log('=== 显示文档列表 ===');
        console.log('文档数量:', documents ? documents.length : 0);

        const tbody = document.getElementById('documentsTableBody');
        if (!tbody) {
            console.error('❌ 未找到文档表格体元素');
            return;
        }

        // 使用DocumentFragment优化DOM操作，减少重排
        const fragment = document.createDocumentFragment();

        if (!documents || documents.length === 0) {
            const emptyRow = document.createElement('tr');
            const emptyCell = document.createElement('td');
            emptyCell.colSpan = 8;
            emptyCell.className = 'text-center text-muted py-5';

            const icon = document.createElement('i');
            icon.className = 'fas fa-folder-open fa-3x mb-3 text-secondary';

            const title = document.createElement('div');
            title.className = 'h5';
            title.textContent = '暂无文档';

            const subtitle = document.createElement('div');
            subtitle.className = 'text-muted';
            subtitle.textContent = '点击上传按钮添加第一个文档';

            emptyCell.appendChild(icon);
            emptyCell.appendChild(title);
            emptyCell.appendChild(subtitle);
            emptyRow.appendChild(emptyCell);
            fragment.appendChild(emptyRow);
        } else {
            // 批量创建行，减少DOM操作次数
            documents.forEach((doc, index) => {
                try {
                    const row = this.createDocumentRow(doc, index);
                    fragment.appendChild(row);
                } catch (error) {
                    console.error(`❌ 创建文档行失败 (${index + 1}):`, error);
                }
            });
        }

        // 一次性更新DOM，避免多次重排
        tbody.innerHTML = '';
        tbody.appendChild(fragment);

        console.log('✅ 文档列表显示完成');
    }

    createDocumentRow(doc, index) {
        const row = document.createElement('tr');
        row.className = 'document-row';
        row.setAttribute('data-document-id', doc.id);

        // 安全地获取并转义文档属性
        const title = this.escapeHtml(doc.title || '未命名文档');
        const originalFileName = this.escapeHtml(doc.originalFileName || doc.fileName || '未知文件');
        const status = doc.status || 'DRAFT';
        const categoryName = this.escapeHtml(doc.categoryName || doc.category?.name || '未分类');
        const ownerUsername = this.escapeHtml(doc.ownerUsername || doc.owner?.username || '未知用户');
        const fileSize = doc.fileSize || 0;
        const versionNumber = doc.versionNumber || 1;
        const createdAt = doc.createdAt || new Date().toISOString();

        // 使用安全的DOM操作而不是innerHTML
        const titleCell = document.createElement('td');
        const titleDiv = document.createElement('div');
        titleDiv.className = 'd-flex align-items-center';

        const icon = document.createElement('i');
        icon.className = 'fas fa-file-alt me-2 text-primary';

        const contentDiv = document.createElement('div');
        const titleSpan = document.createElement('div');
        titleSpan.className = 'fw-bold text-truncate';
        titleSpan.style.maxWidth = '200px';
        titleSpan.title = title;
        titleSpan.textContent = title;

        const fileNameSpan = document.createElement('small');
        fileNameSpan.className = 'text-muted text-truncate d-block';
        fileNameSpan.style.maxWidth = '200px';
        fileNameSpan.title = originalFileName;
        fileNameSpan.textContent = originalFileName;

        contentDiv.appendChild(titleSpan);
        contentDiv.appendChild(fileNameSpan);
        titleDiv.appendChild(icon);
        titleDiv.appendChild(contentDiv);
        titleCell.appendChild(titleDiv);

        // 类别列
        const categoryCell = document.createElement('td');
        const categorySpan = document.createElement('span');
        categorySpan.className = 'text-truncate d-inline-block';
        categorySpan.style.maxWidth = '120px';
        categorySpan.title = categoryName;
        categorySpan.textContent = categoryName;
        categoryCell.appendChild(categorySpan);

        // 所有者列
        const ownerCell = document.createElement('td');
        const ownerSpan = document.createElement('span');
        ownerSpan.className = 'text-truncate d-inline-block';
        ownerSpan.style.maxWidth = '100px';
        ownerSpan.title = ownerUsername;
        ownerSpan.textContent = ownerUsername;
        ownerCell.appendChild(ownerSpan);

        // 状态列
        const statusCell = document.createElement('td');
        const statusBadge = document.createElement('span');
        statusBadge.className = `badge ${this.getStatusBadgeClass(status)}`;
        statusBadge.textContent = this.getStatusDisplayName(status);
        statusCell.appendChild(statusBadge);

        // 版本列
        const versionCell = document.createElement('td');
        const versionBadge = document.createElement('span');
        versionBadge.className = 'badge bg-secondary';
        versionBadge.textContent = `v${versionNumber}`;
        versionCell.appendChild(versionBadge);

        if (doc.isCurrentVersion) {
            const starIcon = document.createElement('i');
            starIcon.className = 'fas fa-star text-warning ms-1';
            starIcon.title = '当前版本';
            versionCell.appendChild(starIcon);
        }

        // 文件大小列
        const sizeCell = document.createElement('td');
        sizeCell.textContent = this.formatFileSize(fileSize);

        // 创建时间列
        const dateCell = document.createElement('td');
        const dateSmall = document.createElement('small');
        dateSmall.textContent = this.formatDate(createdAt);
        dateCell.appendChild(dateSmall);

        // 操作按钮列
        const actionCell = document.createElement('td');
        const btnGroup = this.createActionButtons(doc.id);
        actionCell.appendChild(btnGroup);

        // 组装行
        row.appendChild(titleCell);
        row.appendChild(categoryCell);
        row.appendChild(ownerCell);
        row.appendChild(statusCell);
        row.appendChild(versionCell);
        row.appendChild(sizeCell);
        row.appendChild(dateCell);
        row.appendChild(actionCell);

        return row;
    }

    // 创建操作按钮组
    createActionButtons(docId) {
        const btnGroup = document.createElement('div');
        btnGroup.className = 'btn-group btn-group-sm';
        btnGroup.setAttribute('role', 'group');

        const buttons = [
            { icon: 'fas fa-eye', title: '查看详情', class: 'btn-outline-primary', action: 'viewDocument' },
            { icon: 'fas fa-download', title: '下载文档', class: 'btn-outline-success', action: 'downloadDocument' },
            { icon: 'fas fa-search', title: '预览文档', class: 'btn-outline-info', action: 'previewDocument' },
            { icon: 'fas fa-history', title: '版本历史', class: 'btn-outline-warning', action: 'showVersions' },
            { icon: 'fas fa-trash', title: '删除文档', class: 'btn-outline-danger', action: 'deleteDocument' }
        ];

        buttons.forEach(btn => {
            const button = document.createElement('button');
            button.className = `btn ${btn.class}`;
            button.title = btn.title;
            button.addEventListener('click', () => this[btn.action](docId));

            const icon = document.createElement('i');
            icon.className = btn.icon;
            button.appendChild(icon);

            btnGroup.appendChild(button);
        });

        return btnGroup;
    }

    async handleUpload(e) {
        e.preventDefault();
        this.logger.info('开始处理文档上传');

        // 防止重复提交
        if (this.isUploading) {
            this.logger.warn('上传正在进行中，忽略重复请求');
            this.showNotification('文档正在上传中，请稍候...', 'warning');
            return;
        }

        const form = e.target;
        const fileInput = document.getElementById('documentFile');
        const titleInput = document.getElementById('documentTitle');

        // 验证文件
        if (!fileInput || !fileInput.files[0]) {
            this.logger.error('未选择文件');
            this.showNotification('请选择要上传的文件', 'warning');
            return;
        }

        // 验证标题
        if (!titleInput || !titleInput.value.trim()) {
            this.logger.error('未填写标题');
            this.showNotification('请填写文档标题', 'warning');
            titleInput?.focus();
            return;
        }

        const file = fileInput.files[0];
        this.logger.info('文件信息', {
            name: file.name,
            size: file.size,
            type: file.type
        });

        // 文件大小检查 - 支持1GB大文件
        const maxSize = 1024 * 1024 * 1024; // 1GB
        if (file.size > maxSize) {
            this.logger.error('文件大小超限', { size: file.size });
            this.showNotification('文件大小不能超过1GB', 'error');
            return;
        }

        // 判断是否需要分片上传（大于100MB使用分片）
        const chunkSize = 100 * 1024 * 1024; // 100MB
        const needChunking = file.size > chunkSize;

        // 文件类型检查
        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
            'image/jpeg',
            'image/png'
        ];

        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(pdf|doc|docx|xls|xlsx|txt|jpg|jpeg|png)$/i)) {
            this.logger.error('不支持的文件类型', { type: file.type });
            this.showNotification('不支持的文件类型，请选择PDF、Word、Excel、文本或图片文件', 'error');
            return;
        }

        try {
            this.isUploading = true;
            this.setButtonState('upload', 'loading');
            this.showUploadProgress(true);

            let result;
            if (needChunking) {
                this.logger.info('开始分片上传', { fileSize: file.size, chunkSize });
                result = await this.uploadFileInChunks(file, form);
            } else {
                this.logger.info('开始普通上传', { fileSize: file.size });
                result = await this.uploadFileNormally(form);
            }

            this.logger.info('上传成功', result);
            this.showNotification('文档上传成功！', 'success');
            this.setButtonState('upload', 'success');

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('uploadDocumentModal'));
            if (modal) {
                modal.hide();
            }

            // 延迟刷新列表
            setTimeout(() => {
                this.refreshDocuments();
            }, 500);

        } catch (error) {
            this.logger.error('上传异常', error);
            this.showNotification('上传失败: ' + error.message, 'error');
            this.setButtonState('upload', 'error');
        } finally {
            this.isUploading = false;
            setTimeout(() => {
                this.setButtonState('upload', 'normal');
                this.showUploadProgress(false);
            }, 2000);
        }
    }

    // 工具方法
    getStatusBadgeClass(status) {
        const classes = {
            'DRAFT': 'bg-secondary',
            'UNDER_REVIEW': 'bg-warning',
            'APPROVED': 'bg-success',
            'PUBLISHED': 'bg-primary',
            'ARCHIVED': 'bg-dark'
        };
        return classes[status] || 'bg-secondary';
    }

    getStatusDisplayName(status) {
        const names = {
            'DRAFT': '草稿',
            'UNDER_REVIEW': '审核中',
            'APPROVED': '已批准',
            'PUBLISHED': '已发布',
            'ARCHIVED': '已归档'
        };
        return names[status] || status;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        if (!dateString) return '未知时间';
        try {
            return new Date(dateString).toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return '无效日期';
        }
    }

    showNotification(message, type = 'info') {
        if (typeof UIEnhancements !== 'undefined') {
            UIEnhancements.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    showLoadingState() {
        const tbody = document.getElementById('documentsTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载文档...</div>
                    </td>
                </tr>
            `;
        }
    }

    hideLoadingState() {
        // 加载状态会被实际数据替换，这里不需要特别处理
    }

    showUploadProgress(show) {
        const submitBtn = document.querySelector('#uploadDocumentForm button[type="submit"]');
        if (submitBtn) {
            if (show) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>上传中...';
            } else {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i>上传文档';
            }
        }
    }

    // 按钮状态管理
    setButtonState(buttonId, state) {
        const button = document.querySelector(`[data-button-id="${buttonId}"]`) ||
                      document.querySelector('#uploadDocumentForm button[type="submit"]');

        if (!button) return;

        // 保存原始状态
        if (!this.buttonStates.has(buttonId)) {
            this.buttonStates.set(buttonId, {
                originalText: button.innerHTML,
                originalDisabled: button.disabled
            });
        }

        const originalState = this.buttonStates.get(buttonId);

        switch (state) {
            case 'loading':
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理中...';
                button.classList.add('btn-loading');
                break;
            case 'success':
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-check me-2"></i>成功';
                button.classList.remove('btn-loading');
                button.classList.add('btn-success');
                break;
            case 'error':
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-times me-2"></i>失败';
                button.classList.remove('btn-loading');
                button.classList.add('btn-danger');
                break;
            case 'normal':
            default:
                button.disabled = originalState.originalDisabled;
                button.innerHTML = originalState.originalText;
                button.classList.remove('btn-loading', 'btn-success', 'btn-danger');
                break;
        }
    }

    // 重置所有按钮状态
    resetAllButtonStates() {
        this.buttonStates.forEach((originalState, buttonId) => {
            this.setButtonState(buttonId, 'normal');
        });
        this.buttonStates.clear();
    }

    handleLoadError(error) {
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            console.log(`⚠️ 重试加载文档 (${this.retryCount}/${this.maxRetries})`);
            setTimeout(() => this.loadDocuments(), 1000 * this.retryCount);
        } else {
            const tbody = document.getElementById('documentsTableBody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center text-danger py-4">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                            <div>加载失败: ${error.message}</div>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="documentManager.refreshDocuments()">
                                <i class="fas fa-redo me-1"></i>重试
                            </button>
                        </td>
                    </tr>
                `;
            }
        }
    }

    refreshDocuments() {
        this.retryCount = 0;
        this.loadDocuments();
    }

    clearSearch() {
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.reset();
            this.currentPage = 0;
            this.loadDocuments();
        }
    }

    // 占位方法，将在后续实现
    async loadCategories() {
        // TODO: 实现分类加载
    }

    async loadUsers() {
        // TODO: 实现用户加载
    }

    updatePagination(pageData) {
        // TODO: 实现分页更新
    }

    async viewDocument(id) {
        // TODO: 实现文档查看
    }

    async downloadDocument(id) {
        try {
            console.log('=== 开始下载文档 ===');
            console.log('文档ID:', id);

            const response = await authUtils.secureApiCall(`/dms/api/documents/${id}/download`, {
                method: 'GET'
            });

            if (response.ok) {
                // 获取文件名
                const contentDisposition = response.headers.get('Content-Disposition');
                let fileName = 'download';

                if (contentDisposition) {
                    const fileNameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)/);
                    if (fileNameMatch) {
                        fileName = decodeURIComponent(fileNameMatch[1]);
                    } else {
                        const simpleMatch = contentDisposition.match(/filename="(.+)"/);
                        if (simpleMatch) {
                            fileName = simpleMatch[1];
                        }
                    }
                }

                // 创建下载链接
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                this.showNotification('文档下载成功！', 'success');
                console.log('✅ 文档下载完成');
            } else {
                const errorText = await response.text();
                console.error('❌ 下载失败:', errorText);
                this.showNotification('下载失败: ' + errorText, 'error');
            }
        } catch (error) {
            console.error('❌ 下载异常:', error);
            this.showNotification('下载失败: ' + error.message, 'error');
        }
    }

    async previewDocument(id) {
        try {
            this.logger.info('开始预览文档', { documentId: id });

            // 获取文档信息
            const docResponse = await authUtils.secureApiCall(`/dms/api/documents/${id}`, {
                method: 'GET'
            });

            if (!docResponse.ok) {
                throw new Error('获取文档信息失败');
            }

            const docData = await docResponse.json();
            const document = docData.data;

            // 检查文件类型是否支持预览
            const supportedTypes = [
                'application/pdf',
                'text/plain',
                'text/html',
                'text/css',
                'text/javascript',
                'application/json',
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp'
            ];

            const isSupported = supportedTypes.includes(document.mimeType) ||
                               document.originalFileName.match(/\.(txt|html|css|js|json|pdf|jpg|jpeg|png|gif|webp)$/i);

            if (!isSupported) {
                this.showNotification('此文件类型不支持预览', 'warning');
                return;
            }

            // 创建预览模态框
            this.createPreviewModal(document);

            // 加载预览内容
            const previewResponse = await authUtils.secureApiCall(`/dms/api/documents/${id}/preview`, {
                method: 'GET'
            });

            if (previewResponse.ok) {
                await this.displayPreviewContent(previewResponse, document);
                this.logger.info('文档预览成功', { documentId: id });
            } else {
                throw new Error(`预览失败: ${previewResponse.status}`);
            }

        } catch (error) {
            this.logger.error('文档预览失败', error);
            this.showNotification('预览失败: ' + error.message, 'error');
        }
    }

    // 创建预览模态框
    createPreviewModal(document) {
        // 移除已存在的预览模态框
        const existingModal = document.getElementById('documentPreviewModal');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'documentPreviewModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-eye me-2"></i>
                            文档预览 - ${this.escapeHtml(document.title)}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-0" style="height: 70vh;">
                        <div id="previewContent" class="h-100 d-flex align-items-center justify-content-center">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载预览...</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <div class="me-auto">
                            <small class="text-muted">
                                文件大小: ${this.formatFileSize(document.fileSize)} |
                                类型: ${document.mimeType}
                            </small>
                        </div>
                        <button type="button" class="btn btn-success" onclick="documentManager.downloadDocument(${document.id})">
                            <i class="fas fa-download me-1"></i>下载
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 显示模态框
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // 模态框关闭时清理
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    // 显示预览内容
    async displayPreviewContent(response, document) {
        const previewContent = document.getElementById('previewContent');
        const mimeType = document.mimeType;

        try {
            if (mimeType === 'application/pdf') {
                // PDF预览
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                previewContent.innerHTML = `
                    <iframe src="${url}" class="w-100 h-100" style="border: none;"></iframe>
                `;
            } else if (mimeType.startsWith('image/')) {
                // 图片预览
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                previewContent.innerHTML = `
                    <div class="text-center h-100 d-flex align-items-center justify-content-center">
                        <img src="${url}" class="img-fluid" style="max-height: 100%; max-width: 100%;" alt="预览图片">
                    </div>
                `;
            } else if (mimeType.startsWith('text/')) {
                // 文本文件预览
                const text = await response.text();
                previewContent.innerHTML = `
                    <div class="p-3 h-100">
                        <pre class="h-100 overflow-auto"><code>${this.escapeHtml(text)}</code></pre>
                    </div>
                `;
            } else {
                // 其他类型，尝试作为文本显示
                const text = await response.text();
                previewContent.innerHTML = `
                    <div class="p-3 h-100">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            此文件类型可能无法正确预览，以下是文件内容：
                        </div>
                        <pre class="h-75 overflow-auto"><code>${this.escapeHtml(text)}</code></pre>
                    </div>
                `;
            }
        } catch (error) {
            previewContent.innerHTML = `
                <div class="p-3 text-center">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        预览失败: ${error.message}
                    </div>
                </div>
            `;
        }
    }

    async showVersions(id) {
        // TODO: 实现版本历史
    }

    async deleteDocument(id) {
        this.logger.info('开始删除文档', { documentId: id });

        // 获取文档信息用于确认弹窗
        let documentTitle = '未知文档';
        try {
            const docResponse = await authUtils.secureApiCall(`/dms/api/documents/${id}`, {
                method: 'GET'
            });
            if (docResponse.ok) {
                const docData = await docResponse.json();
                documentTitle = docData.data.title || '未知文档';
            }
        } catch (error) {
            this.logger.warn('获取文档信息失败，使用默认标题', error);
        }

        // 显示现代化确认弹窗
        const confirmed = await this.showDeleteConfirmDialog(documentTitle);
        if (!confirmed) {
            this.logger.info('用户取消删除操作');
            return;
        }

        try {
            // 设置删除状态
            this.setButtonState(`delete-${id}`, 'loading');

            const response = await authUtils.secureApiCall(`/dms/api/documents/${id}`, {
                method: 'DELETE'
            });

            this.logger.info('删除API响应', { status: response.status });

            if (response.ok) {
                const result = await response.json();
                this.logger.info('删除成功', result);

                // 实时从列表中移除
                this.removeDocumentFromList(id);

                this.showNotification('文档删除成功！', 'success');
                this.setButtonState(`delete-${id}`, 'success');

                // 记录用户操作
                this.logger.userAction('删除文档', { documentId: id, title: documentTitle });

            } else {
                const errorData = await response.json();
                throw new Error(errorData.message || `HTTP ${response.status}`);
            }
        } catch (error) {
            this.logger.error('删除文档失败', error);
            this.showNotification('删除失败: ' + error.message, 'error');
            this.setButtonState(`delete-${id}`, 'error');
        } finally {
            // 恢复按钮状态
            setTimeout(() => {
                this.setButtonState(`delete-${id}`, 'normal');
            }, 2000);
        }
    }

    // 显示删除确认对话框
    async showDeleteConfirmDialog(documentTitle) {
        return new Promise((resolve) => {
            // 创建现代化确认弹窗
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'deleteConfirmModal';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                确认删除文档
                            </h5>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-3">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <h6>您确定要删除以下文档吗？</h6>
                                <p class="fw-bold text-primary">${this.escapeHtml(documentTitle)}</p>
                                <div class="alert alert-warning">
                                    <i class="fas fa-info-circle me-2"></i>
                                    删除后文档将被移至回收站，可以在回收站中恢复
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-action="cancel">
                                <i class="fas fa-times me-2"></i>取消
                            </button>
                            <button type="button" class="btn btn-danger" data-action="confirm">
                                <i class="fas fa-trash me-2"></i>确认删除
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 绑定事件
            modal.querySelector('[data-action="cancel"]').addEventListener('click', () => {
                resolve(false);
                this.closeModal(modal);
            });

            modal.querySelector('[data-action="confirm"]').addEventListener('click', () => {
                resolve(true);
                this.closeModal(modal);
            });

            // 显示模态框
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // 模态框关闭时清理
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        });
    }

    // 关闭模态框
    closeModal(modal) {
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        }
    }

    // 实时从列表中移除文档
    removeDocumentFromList(documentId) {
        const row = document.querySelector(`tr[data-document-id="${documentId}"]`);
        if (row) {
            // 添加淡出动画
            row.style.transition = 'opacity 0.3s ease';
            row.style.opacity = '0';

            setTimeout(() => {
                row.remove();
                this.logger.info('文档已从列表中移除', { documentId });

                // 检查是否需要重新加载页面（如果当前页没有文档了）
                const tbody = document.getElementById('documentsTableBody');
                if (tbody && tbody.children.length === 0) {
                    this.logger.info('当前页无文档，重新加载');
                    this.loadDocuments();
                }
            }, 300);
        }
    }

    // 普通文件上传
    async uploadFileNormally(form) {
        const formData = new FormData(form);
        this.logger.info('FormData创建完成');

        const response = await authUtils.secureApiCall('/dms/api/documents/upload', {
            method: 'POST',
            body: formData
        });

        this.logger.info('上传请求响应', { status: response ? response.status : 'null' });

        if (response && response.ok) {
            return await response.json();
        } else if (response) {
            try {
                const errorData = await response.json();
                throw new Error(errorData.message || errorData.error || `HTTP ${response.status}`);
            } catch (jsonError) {
                const errorText = await response.text();
                throw new Error(errorText || `HTTP ${response.status}`);
            }
        } else {
            throw new Error('网络连接失败，请检查网络状态');
        }
    }

    // 分片文件上传
    async uploadFileInChunks(file, form) {
        const chunkSize = 100 * 1024 * 1024; // 100MB per chunk
        const totalChunks = Math.ceil(file.size / chunkSize);
        const uploadId = this.generateUploadId();

        this.logger.info('分片上传参数', {
            totalChunks,
            chunkSize,
            uploadId,
            fileName: file.name
        });

        // 获取表单数据
        const formData = new FormData(form);
        const title = formData.get('title');
        const description = formData.get('description');
        const categoryId = formData.get('categoryId');

        // 上传所有分片
        for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
            const start = chunkIndex * chunkSize;
            const end = Math.min(start + chunkSize, file.size);
            const chunk = file.slice(start, end);

            await this.uploadChunk(chunk, chunkIndex, totalChunks, uploadId, {
                fileName: file.name,
                title,
                description,
                categoryId
            });

            // 更新进度
            const progress = ((chunkIndex + 1) / totalChunks) * 100;
            this.updateUploadProgress(progress);
        }

        // 完成分片上传
        return await this.completeChunkedUpload(uploadId, {
            fileName: file.name,
            title,
            description,
            categoryId,
            totalChunks
        });
    }

    // 上传单个分片
    async uploadChunk(chunk, chunkIndex, totalChunks, uploadId, metadata) {
        const chunkFormData = new FormData();
        chunkFormData.append('chunk', chunk);
        chunkFormData.append('chunkIndex', chunkIndex);
        chunkFormData.append('totalChunks', totalChunks);
        chunkFormData.append('uploadId', uploadId);
        chunkFormData.append('fileName', metadata.fileName);

        this.logger.info(`上传分片 ${chunkIndex + 1}/${totalChunks}`, {
            chunkSize: chunk.size,
            uploadId
        });

        const response = await authUtils.secureApiCall('/dms/api/documents/upload-chunk', {
            method: 'POST',
            body: chunkFormData
        });

        if (!response || !response.ok) {
            if (response) {
                try {
                    const errorData = await response.json();
                    throw new Error(`分片 ${chunkIndex + 1} 上传失败: ${errorData.message || errorData.error}`);
                } catch (jsonError) {
                    const errorText = await response.text();
                    throw new Error(`分片 ${chunkIndex + 1} 上传失败: ${errorText}`);
                }
            } else {
                throw new Error(`分片 ${chunkIndex + 1} 上传失败: 网络连接失败`);
            }
        }

        return await response.json();
    }

    // 完成分片上传
    async completeChunkedUpload(uploadId, metadata) {
        const completeData = new FormData();
        completeData.append('uploadId', uploadId);
        completeData.append('fileName', metadata.fileName);
        completeData.append('title', metadata.title);
        completeData.append('description', metadata.description || '');
        completeData.append('categoryId', metadata.categoryId || '');
        completeData.append('totalChunks', metadata.totalChunks);

        this.logger.info('完成分片上传', { uploadId, totalChunks: metadata.totalChunks });

        const response = await authUtils.secureApiCall('/dms/api/documents/complete-upload', {
            method: 'POST',
            body: completeData
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`完成上传失败: ${errorText}`);
        }

        return await response.json();
    }

    // 生成上传ID
    generateUploadId() {
        return 'upload_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 更新上传进度
    updateUploadProgress(percentage) {
        const progressBar = document.getElementById('uploadProgressBar');
        const progressText = document.getElementById('uploadProgressText');

        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }

        if (progressText) {
            progressText.textContent = `上传进度: ${Math.round(percentage)}%`;
        }
    }

    // 重置上传进度
    resetUploadProgress() {
        this.updateUploadProgress(0);
        const progressContainer = document.getElementById('uploadProgressContainer');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.documentManager = new DocumentManager();
    console.log('✅ 文档管理器已初始化');
});
