<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>🧪 用户管理API测试</h1>
    
    <div class="test-section">
        <h3>1. 认证状态检查</h3>
        <button class="btn-primary" onclick="checkAuth()">检查认证状态</button>
        <div id="authStatus"></div>
    </div>

    <div class="test-section">
        <h3>2. API测试</h3>
        <button class="btn-success" onclick="testUsersAPI()">测试用户列表API</button>
        <button class="btn-warning" onclick="testActiveUsersAPI()">测试活跃用户API</button>
        <button class="btn-primary" onclick="testUserStatsAPI()">测试用户统计API</button>
        <button class="btn-danger" onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-section">
        <h3>3. 用户列表显示</h3>
        <div id="userTableContainer">
            <table id="userTable" style="display: none;">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>姓名</th>
                        <th>部门</th>
                        <th>角色</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody id="userTableBody">
                </tbody>
            </table>
        </div>
    </div>

    <div class="test-section">
        <h3>4. 测试日志</h3>
        <div id="testLog" class="log"></div>
    </div>

    <script src="/dms/js/auth.js"></script>
    <script>
        let logElement = document.getElementById('testLog');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            logElement.innerHTML = '';
        }

        async function checkAuth() {
            log('=== 检查认证状态 ===', 'info');
            
            try {
                authUtils.initAuth();
                
                if (authUtils.isLoggedIn()) {
                    const user = authUtils.getCurrentUser();
                    log(`✅ 用户已登录: ${user.username}`, 'success');
                    log(`   - 角色: ${user.roles.join(', ')}`, 'info');
                    log(`   - 邮箱: ${user.email}`, 'info');
                    
                    // 检查权限
                    const hasAdminRole = user.roles.includes('ROLE_ADMIN');
                    const hasQARole = user.roles.includes('ROLE_QA');
                    
                    if (hasAdminRole || hasQARole) {
                        log(`✅ 用户有访问用户管理的权限`, 'success');
                        document.getElementById('authStatus').innerHTML = 
                            `<span class="success">✅ 已登录: ${user.username} (有权限)</span>`;
                    } else {
                        log(`⚠️ 用户没有访问用户管理的权限`, 'warning');
                        document.getElementById('authStatus').innerHTML = 
                            `<span class="warning">⚠️ 已登录: ${user.username} (无权限)</span>`;
                    }
                } else {
                    log('❌ 用户未登录', 'error');
                    document.getElementById('authStatus').innerHTML = 
                        '<span class="error">❌ 未登录</span>';
                }
            } catch (error) {
                log(`❌ 认证检查失败: ${error.message}`, 'error');
            }
        }

        async function testUsersAPI() {
            log('=== 测试用户列表API ===', 'info');
            
            if (!authUtils.isLoggedIn()) {
                log('❌ 用户未登录，无法测试API', 'error');
                return;
            }
            
            try {
                log('🌐 调用 /dms/api/users...', 'info');
                const response = await authUtils.secureApiCall('/dms/api/users');
                
                log(`📨 响应状态: ${response ? response.status : 'null'}`, 'info');
                
                if (response && response.ok) {
                    const result = await response.json();
                    log('✅ API调用成功', 'success');
                    log(`   - 响应结构: ${JSON.stringify(Object.keys(result), null, 2)}`, 'info');
                    log(`   - success: ${result.success}`, 'info');
                    log(`   - message: ${result.message}`, 'info');
                    
                    if (result.data) {
                        log(`   - data类型: ${typeof result.data}`, 'info');
                        log(`   - data键: ${JSON.stringify(Object.keys(result.data), null, 2)}`, 'info');
                        
                        if (result.data.content) {
                            log(`   - content长度: ${result.data.content.length}`, 'info');
                            displayUsers(result.data.content);
                        } else if (Array.isArray(result.data)) {
                            log(`   - data数组长度: ${result.data.length}`, 'info');
                            displayUsers(result.data);
                        }
                    }
                } else if (response) {
                    const errorData = await response.json();
                    log(`❌ API调用失败: ${response.status}`, 'error');
                    log(`   - 错误信息: ${errorData.message || errorData.error}`, 'error');
                } else {
                    log('❌ 网络连接失败', 'error');
                }
            } catch (error) {
                log(`💥 API测试异常: ${error.message}`, 'error');
            }
        }

        async function testActiveUsersAPI() {
            log('=== 测试活跃用户API ===', 'info');
            
            try {
                log('🌐 调用 /dms/api/users/active...', 'info');
                const response = await authUtils.secureApiCall('/dms/api/users/active');
                
                if (response && response.ok) {
                    const result = await response.json();
                    log('✅ 活跃用户API调用成功', 'success');
                    log(`   - 活跃用户数量: ${result.data ? result.data.length : 0}`, 'info');
                } else if (response) {
                    const errorData = await response.json();
                    log(`❌ 活跃用户API失败: ${errorData.message}`, 'error');
                } else {
                    log('❌ 网络连接失败', 'error');
                }
            } catch (error) {
                log(`💥 活跃用户API异常: ${error.message}`, 'error');
            }
        }

        async function testUserStatsAPI() {
            log('=== 测试用户统计API ===', 'info');
            
            try {
                log('🌐 调用 /dms/api/users/stats...', 'info');
                const response = await authUtils.secureApiCall('/dms/api/users/stats');
                
                if (response && response.ok) {
                    const result = await response.json();
                    log('✅ 用户统计API调用成功', 'success');
                    log(`   - 统计数据: ${JSON.stringify(result.data, null, 2)}`, 'info');
                } else if (response) {
                    const errorData = await response.json();
                    log(`❌ 用户统计API失败: ${errorData.message}`, 'error');
                } else {
                    log('❌ 网络连接失败', 'error');
                }
            } catch (error) {
                log(`💥 用户统计API异常: ${error.message}`, 'error');
            }
        }

        function displayUsers(users) {
            const table = document.getElementById('userTable');
            const tbody = document.getElementById('userTableBody');
            
            if (!Array.isArray(users) || users.length === 0) {
                table.style.display = 'none';
                log('⚠️ 没有用户数据可显示', 'warning');
                return;
            }
            
            tbody.innerHTML = '';
            
            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.id || 'N/A'}</td>
                    <td>${user.username || 'N/A'}</td>
                    <td>${user.email || 'N/A'}</td>
                    <td>${(user.firstName || '') + ' ' + (user.lastName || '')}</td>
                    <td>${user.department ? user.department.name : 'N/A'}</td>
                    <td>${user.roles ? user.roles.map(r => r.name.replace('ROLE_', '')).join(', ') : 'N/A'}</td>
                    <td>${user.isActive ? '活跃' : '禁用'}</td>
                `;
                tbody.appendChild(row);
            });
            
            table.style.display = 'table';
            log(`✅ 显示了 ${users.length} 个用户`, 'success');
        }

        // 页面加载时自动检查认证状态
        window.addEventListener('load', function() {
            log('🚀 页面加载完成，开始测试...', 'info');
            checkAuth();
        });
    </script>
</body>
</html>
