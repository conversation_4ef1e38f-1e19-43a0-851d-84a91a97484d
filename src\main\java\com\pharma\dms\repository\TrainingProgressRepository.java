package com.pharma.dms.repository;

import com.pharma.dms.entity.TrainingProgress;
import com.pharma.dms.entity.TrainingRecord;
import com.pharma.dms.entity.TrainingCourse;
import com.pharma.dms.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TrainingProgressRepository extends JpaRepository<TrainingProgress, Long> {

    // 根据培训记录查找进度
    Optional<TrainingProgress> findByTrainingRecord(TrainingRecord trainingRecord);
    
    // 根据用户查找所有进度
    List<TrainingProgress> findByTrainingRecordUser(User user);
    
    // 根据课程查找所有进度
    List<TrainingProgress> findByTrainingRecordCourse(TrainingCourse course);
    
    // 根据会话ID查找进度
    Optional<TrainingProgress> findBySessionId(String sessionId);
    
    // 查找活跃的学习会话
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.sessionId IS NOT NULL AND tp.endTime IS NULL AND tp.lastAccessed > :cutoffTime")
    List<TrainingProgress> findActiveSessions(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    // 查找超时的学习会话
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.sessionId IS NOT NULL AND tp.endTime IS NULL AND tp.lastAccessed < :timeoutTime")
    List<TrainingProgress> findTimeoutSessions(@Param("timeoutTime") LocalDateTime timeoutTime);

    // 分页查询
    Page<TrainingProgress> findByTrainingRecordUser(User user, Pageable pageable);
    
    Page<TrainingProgress> findByTrainingRecordCourse(TrainingCourse course, Pageable pageable);

    // 完成状态查询
    List<TrainingProgress> findByIsCompletedTrue();
    
    List<TrainingProgress> findByIsCompletedFalse();
    
    List<TrainingProgress> findByTrainingRecordUserAndIsCompleted(User user, Boolean isCompleted);

    // 进度范围查询
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.progressPercentage >= :minProgress AND tp.progressPercentage <= :maxProgress")
    List<TrainingProgress> findByProgressRange(@Param("minProgress") Integer minProgress, @Param("maxProgress") Integer maxProgress);

    // 时间范围查询
    List<TrainingProgress> findByLastAccessedBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<TrainingProgress> findByCompletionDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    // 统计查询
    @Query("SELECT COUNT(tp) FROM TrainingProgress tp WHERE tp.trainingRecord.course = :course AND tp.isCompleted = true")
    Long countCompletedByCourse(@Param("course") TrainingCourse course);
    
    @Query("SELECT COUNT(tp) FROM TrainingProgress tp WHERE tp.trainingRecord.user = :user AND tp.isCompleted = true")
    Long countCompletedByUser(@Param("user") User user);
    
    @Query("SELECT AVG(tp.progressPercentage) FROM TrainingProgress tp WHERE tp.trainingRecord.course = :course")
    Double getAverageProgressByCourse(@Param("course") TrainingCourse course);
    
    @Query("SELECT AVG(tp.timeSpentMinutes) FROM TrainingProgress tp WHERE tp.trainingRecord.course = :course AND tp.isCompleted = true")
    Double getAverageTimeSpentByCourse(@Param("course") TrainingCourse course);

    // 学习效率查询
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.timeSpentMinutes > 0 ORDER BY (tp.progressPercentage * 1.0 / tp.timeSpentMinutes) DESC")
    List<TrainingProgress> findMostEfficientLearners(Pageable pageable);
    
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.isCompleted = true ORDER BY tp.timeSpentMinutes ASC")
    List<TrainingProgress> findFastestCompletions(Pageable pageable);

    // 最近活动查询
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.lastAccessed >= :since ORDER BY tp.lastAccessed DESC")
    List<TrainingProgress> findRecentActivity(@Param("since") LocalDateTime since, Pageable pageable);
    
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.completionDate >= :since ORDER BY tp.completionDate DESC")
    List<TrainingProgress> findRecentCompletions(@Param("since") LocalDateTime since, Pageable pageable);

    // 学习模式分析
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.isMobileDevice = :isMobile")
    List<TrainingProgress> findByDeviceType(@Param("isMobile") Boolean isMobile);
    
    @Query("SELECT tp.deviceType, COUNT(tp) FROM TrainingProgress tp WHERE tp.deviceType IS NOT NULL GROUP BY tp.deviceType")
    List<Object[]> getDeviceTypeStatistics();

    // 书签和笔记查询
    List<TrainingProgress> findByBookmarkPositionIsNotNull();
    
    List<TrainingProgress> findByNotesIsNotNull();
    
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.lastBookmarkTime >= :since")
    List<TrainingProgress> findRecentBookmarks(@Param("since") LocalDateTime since);

    // 交互统计
    @Query("SELECT AVG(tp.interactionCount) FROM TrainingProgress tp WHERE tp.trainingRecord.course = :course")
    Double getAverageInteractionsByCourse(@Param("course") TrainingCourse course);
    
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.interactionCount > :threshold")
    List<TrainingProgress> findHighInteractionSessions(@Param("threshold") Integer threshold);

    // 学习质量评估
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.difficultyRating IS NOT NULL AND tp.satisfactionRating IS NOT NULL")
    List<TrainingProgress> findWithRatings();
    
    @Query("SELECT AVG(tp.satisfactionRating) FROM TrainingProgress tp WHERE tp.trainingRecord.course = :course AND tp.satisfactionRating IS NOT NULL")
    Double getAverageSatisfactionByCourse(@Param("course") TrainingCourse course);

    // 清理和维护
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.sessionId IS NOT NULL AND tp.lastAccessed < :expiredTime")
    List<TrainingProgress> findExpiredSessions(@Param("expiredTime") LocalDateTime expiredTime);
    
    @Query("DELETE FROM TrainingProgress tp WHERE tp.sessionId IS NOT NULL AND tp.lastAccessed < :cleanupTime")
    void cleanupExpiredSessions(@Param("cleanupTime") LocalDateTime cleanupTime);

    // 自定义查询方法
    @Query("SELECT tp FROM TrainingProgress tp WHERE " +
           "(:userId IS NULL OR tp.trainingRecord.user.id = :userId) AND " +
           "(:courseId IS NULL OR tp.trainingRecord.course.id = :courseId) AND " +
           "(:isCompleted IS NULL OR tp.isCompleted = :isCompleted) AND " +
           "(:minProgress IS NULL OR tp.progressPercentage >= :minProgress) AND " +
           "(:maxProgress IS NULL OR tp.progressPercentage <= :maxProgress)")
    Page<TrainingProgress> findProgressWithFilters(@Param("userId") Long userId,
                                                  @Param("courseId") Long courseId,
                                                  @Param("isCompleted") Boolean isCompleted,
                                                  @Param("minProgress") Integer minProgress,
                                                  @Param("maxProgress") Integer maxProgress,
                                                  Pageable pageable);

    // 学习路径分析
    @Query("SELECT tp FROM TrainingProgress tp WHERE tp.learningPath IS NOT NULL")
    List<TrainingProgress> findWithLearningPath();
    
    // 视频和文档学习时间统计
    @Query("SELECT SUM(tp.videoWatchTimeSeconds) FROM TrainingProgress tp WHERE tp.trainingRecord.course = :course")
    Long getTotalVideoWatchTimeByCourse(@Param("course") TrainingCourse course);
    
    @Query("SELECT SUM(tp.documentReadTimeSeconds) FROM TrainingProgress tp WHERE tp.trainingRecord.course = :course")
    Long getTotalDocumentReadTimeByCourse(@Param("course") TrainingCourse course);

    // 学习会话分析
    @Query("SELECT COUNT(DISTINCT tp.sessionId) FROM TrainingProgress tp WHERE tp.trainingRecord.user = :user AND tp.sessionId IS NOT NULL")
    Long countSessionsByUser(@Param("user") User user);
    
    @Query("SELECT tp.sessionId, COUNT(tp) FROM TrainingProgress tp WHERE tp.sessionId IS NOT NULL GROUP BY tp.sessionId HAVING COUNT(tp) > 1")
    List<Object[]> findMultipleProgressPerSession();
}
